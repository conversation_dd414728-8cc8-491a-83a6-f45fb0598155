#!/bin/bash

# MATRIX_IDE UI Component Testing Script
# Phase 1.5 - Comprehensive UI Testing Automation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$TEST_DIR")"
RESULTS_DIR="$PROJECT_ROOT/test_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$RESULTS_DIR/test_run_$TIMESTAMP.log"

# Create results directory
mkdir -p "$RESULTS_DIR"

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_SKIPPED=0

# Test result tracking
declare -a FAILED_TESTS
declare -a PASSED_TESTS

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_exit_code="${3:-0}"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    log "Running test: $test_name"
    
    if eval "$test_command"; then
        if [ $? -eq $expected_exit_code ]; then
            log_success "$test_name"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            PASSED_TESTS+=("$test_name")
        else
            log_error "$test_name (unexpected exit code)"
            TESTS_FAILED=$((TESTS_FAILED + 1))
            FAILED_TESTS+=("$test_name")
        fi
    else
        log_error "$test_name (execution failed)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        FAILED_TESTS+=("$test_name")
    fi
}

# Function to check if MATRIX_IDE builds successfully
test_build() {
    log "=== BUILD VERIFICATION ==="
    
    cd "$PROJECT_ROOT"
    
    run_test "Cargo Check - All Crates" "cargo check --all"
    run_test "Cargo Build - Matrix UI" "cargo build -p matrix-ui"
    run_test "Cargo Build - Matrix IDE" "cargo build --bin matrix-ide"
    
    # Check for compilation warnings
    log "Checking for critical warnings..."
    if cargo check --all 2>&1 | grep -i "error\|failed"; then
        log_error "Critical compilation issues found"
        return 1
    else
        log_success "No critical compilation issues"
    fi
}

# Function to test component compilation
test_component_compilation() {
    log "=== COMPONENT COMPILATION TESTING ==="
    
    cd "$PROJECT_ROOT/crates/matrix-ui"
    
    # Test individual component compilation
    run_test "Terminal Component Compilation" "cargo check --lib --features terminal"
    run_test "Status Bar Component Compilation" "cargo check --lib --features status-bar"
    run_test "Properties Panel Compilation" "cargo check --lib --features properties"
    
    # Check for unused imports and dead code
    log "Checking for code quality issues..."
    cargo check 2>&1 | grep -E "unused|dead_code" > "$RESULTS_DIR/code_quality_warnings.txt" || true
    
    local warning_count=$(wc -l < "$RESULTS_DIR/code_quality_warnings.txt")
    if [ "$warning_count" -gt 50 ]; then
        log_warning "High number of code quality warnings: $warning_count"
    else
        log_success "Code quality warnings within acceptable range: $warning_count"
    fi
}

# Function to test Floem 0.2 API compatibility
test_floem_compatibility() {
    log "=== FLOEM 0.2 API COMPATIBILITY ==="
    
    cd "$PROJECT_ROOT/crates/matrix-ui"
    
    # Check for deprecated API usage
    log "Scanning for deprecated Floem API usage..."
    
    # Search for common deprecated patterns
    local deprecated_patterns=(
        "with_alpha_factor"
        "old_signal_api"
        "deprecated_layout"
    )
    
    local deprecated_found=false
    for pattern in "${deprecated_patterns[@]}"; do
        if grep -r "$pattern" src/ > /dev/null 2>&1; then
            log_warning "Found deprecated pattern: $pattern"
            deprecated_found=true
        fi
    done
    
    if [ "$deprecated_found" = false ]; then
        log_success "No deprecated API patterns found"
    fi
    
    # Test reactive system compatibility
    run_test "Reactive System Check" "grep -r 'RwSignal\|create_rw_signal' src/ > /dev/null"
    run_test "Event System Check" "grep -r 'on_click\|on_key' src/ > /dev/null"
    run_test "Layout System Check" "grep -r 'h_stack\|v_stack\|container' src/ > /dev/null"
}

# Function to test memory usage patterns
test_memory_patterns() {
    log "=== MEMORY USAGE PATTERN ANALYSIS ==="
    
    cd "$PROJECT_ROOT/crates/matrix-ui"
    
    # Check for potential memory leaks
    log "Analyzing memory usage patterns..."
    
    # Look for Arc/Rc usage patterns
    local arc_count=$(grep -r "Arc<" src/ | wc -l)
    local rc_count=$(grep -r "Rc<" src/ | wc -l)
    
    log "Arc usage count: $arc_count"
    log "Rc usage count: $rc_count"
    
    # Check for proper cleanup patterns
    if grep -r "drop\|cleanup" src/ > /dev/null; then
        log_success "Cleanup patterns found in code"
    else
        log_warning "Limited cleanup patterns found"
    fi
    
    # Check for signal cleanup
    if grep -r "signal.*drop\|cleanup.*signal" src/ > /dev/null; then
        log_success "Signal cleanup patterns found"
    else
        log_warning "Signal cleanup patterns not found"
    fi
}

# Function to test component structure
test_component_structure() {
    log "=== COMPONENT STRUCTURE VALIDATION ==="
    
    cd "$PROJECT_ROOT/crates/matrix-ui/src"
    
    # Check that all major components exist
    local required_components=(
        "components/terminal_panel.rs"
        "components/status_bar.rs"
        "components/properties_panel.rs"
        "theme/mod.rs"
    )
    
    for component in "${required_components[@]}"; do
        if [ -f "$component" ]; then
            log_success "Component exists: $component"
        else
            log_error "Missing component: $component"
        fi
    done
    
    # Check component exports
    run_test "Component Exports Check" "grep -r 'pub struct\|pub fn\|pub enum' components/ > /dev/null"
    
    # Verify theme integration
    run_test "Theme Integration Check" "grep -r 'ThemeManager\|theme' components/ > /dev/null"
}

# Function to generate test report
generate_report() {
    log "=== GENERATING TEST REPORT ==="
    
    local report_file="$RESULTS_DIR/test_report_$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# MATRIX_IDE UI Component Test Report

**Test Run**: $TIMESTAMP
**Total Tests**: $TESTS_TOTAL
**Passed**: $TESTS_PASSED
**Failed**: $TESTS_FAILED
**Skipped**: $TESTS_SKIPPED

## Test Results Summary

### Passed Tests
EOF

    for test in "${PASSED_TESTS[@]}"; do
        echo "- ✅ $test" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

### Failed Tests
EOF

    for test in "${FAILED_TESTS[@]}"; do
        echo "- ❌ $test" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

## Detailed Logs

See full test log: \`test_run_$TIMESTAMP.log\`

## Recommendations

EOF

    if [ $TESTS_FAILED -eq 0 ]; then
        cat >> "$report_file" << EOF
🎉 All tests passed! The UI components are ready for Phase 2 development.

### Next Steps:
1. Proceed with advanced feature implementation
2. Set up continuous testing pipeline
3. Document component APIs
4. Begin Phase 2 planning
EOF
    else
        cat >> "$report_file" << EOF
⚠️ Some tests failed. Address the following before proceeding:

### Critical Actions:
1. Fix all failed tests
2. Re-run test suite
3. Verify component functionality manually
4. Update documentation as needed

### Failed Test Analysis:
EOF
        for test in "${FAILED_TESTS[@]}"; do
            echo "- **$test**: Requires investigation and fixes" >> "$report_file"
        done
    fi
    
    log "Test report generated: $report_file"
}

# Function to display test summary
display_summary() {
    echo
    echo "=================================="
    echo "    MATRIX_IDE UI TEST SUMMARY"
    echo "=================================="
    echo "Total Tests:  $TESTS_TOTAL"
    echo "Passed:       $TESTS_PASSED"
    echo "Failed:       $TESTS_FAILED"
    echo "Skipped:      $TESTS_SKIPPED"
    echo "Success Rate: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%"
    echo "=================================="
    echo
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "All tests passed! UI components are ready for Phase 2."
        return 0
    else
        log_error "$TESTS_FAILED tests failed. Review and fix before proceeding."
        return 1
    fi
}

# Main test execution
main() {
    log "Starting MATRIX_IDE UI Component Testing - Phase 1.5"
    log "Results will be saved to: $RESULTS_DIR"
    
    # Run test suites
    test_build
    test_component_compilation
    test_floem_compatibility
    test_memory_patterns
    test_component_structure
    
    # Generate report and summary
    generate_report
    display_summary
}

# Execute main function
main "$@"
