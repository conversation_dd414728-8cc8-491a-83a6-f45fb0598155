/// Tipo di pannello per le viste dei plugin
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum PluginPanelType {
    /// Pannello sinistro
    Left,

    /// Pannello destro
    Right,

    /// Pannello inferiore
    Bottom,

    /// Pannello fluttuante
    Float,
}

/**
 * Trait che definisce una vista UI fornita da un plugin.
 *
 * Dipende esclusivamente dalle API pubbliche di `matrix-graphics-api`.
 * Il rendering deve essere effettuato tramite il trait [`MatrixRenderer`](crates/matrix-graphics-api/src/lib.rs:39), senza dipendenze da backend concreti.
 *
 * Ogni implementazione deve garantire la massima modularità e compatibilità con le evoluzioni dell’API grafica.
 */
// Rimossa dipendenza da matrix_graphics_api per ora

/// Trait che definisce una vista UI fornita da un plugin, astratta dal backend grafico.
/// Il rendering avviene tramite il trait MatrixRenderer.
pub trait PluginView: Send + Sync {
    /**
     * Esegue il rendering della vista usando il renderer astratto.
     *
     * Il parametro `renderer` è un trait object [`MatrixRenderer`](crates/matrix-graphics-api/src/lib.rs:39).
     * Le implementazioni devono usare solo i metodi pubblici del trait, senza accedere a tipi o funzioni di backend.
     */
    fn render(&self, renderer: &mut dyn std::any::Any);

    /// Ottiene il tipo di pannello preferito per questa vista
    fn get_panel_type(&self) -> PluginPanelType {
        PluginPanelType::Right // Default panel type
    }

    /// Ottiene il titolo della vista
    fn get_title(&self) -> String;

    /// Ottiene un identificatore univoco per questa vista
    fn get_id(&self) -> String;
}
