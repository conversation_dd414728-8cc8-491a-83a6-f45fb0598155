//! # Matrix Core
//! 
//! Il Core Engine di MATRIX IDE, responsabile per:
//! - Gestione degli eventi attraverso un Event Bus
//! - Gestione dello stato globale
//! - Sistema di plugin
//! - Coordinamento dei componenti

mod engine;
mod event_bus;
mod state;
mod plugin;
mod plugin_view;
mod log_level;
mod error;
mod plugin_loader;
mod api;
pub use api::{OrchestratorAPI, MatrixOrchestrator};
pub mod micro_task_fabric;
pub mod chain_reaction_engine;
pub mod dag_engine;
pub mod executors;
pub use engine::Engine;
pub use event_bus::{EventBus, Event, EventHandler, EventSubscriber};
pub use state::{GlobalState, StateAccess};
pub use plugin::{Plugin, PluginManager, PluginContext, PluginInfo};
pub use plugin_view::{PluginView, PluginPanelType};
pub use log_level::LogLevel;
pub use error::CoreError;
pub use plugin_loader::{<PERSON>lug<PERSON>Loader, PluginLoaderError};

// Re-export dei tipi pubblici principali
pub use micro_task_fabric::{
    MicroTaskFabric, MicroTask, TaskType, TaskStatus, TaskPriority,
    MVPResult, DAGExecutionResult, MicroTaskFabricConfig
};
pub use chain_reaction_engine::{
    ChainReactionEngine, DependencyImpact, ChainReactionResult
};
pub use micro_task_fabric::{ChainReactionAnalysis, AlternativeApproach};
pub use dag_engine::{
    DagEngine, DagGraph, Task, TaskState, TaskMetadata, TaskExecutor
};

/// Versione del Core Engine
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Inizializza il Core Engine con la configurazione predefinita
pub fn init() -> Result<Engine, CoreError> {
    Engine::new()
}

#[cfg(test)]
/*
 * [FIX] Rimosso `mod tests;` per evitare doppia definizione del modulo `tests`.
 * Il modulo di test inline viene mantenuto secondo le best practice Rust.
 */

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_engine_creation() {
        let engine = init();
        assert!(engine.is_ok());
    }
}