//! Plugin Agent Console per MATRIX IDE
//!
//! Questo plugin fornisce una console interattiva per visualizzare
//! i log dell'agente AI e inviare comandi.

use std::collections::VecDeque;
use std::sync::{Arc, Mutex};

use matrix_core::{CoreError, Event, LogLevel, Plugin, PluginInfo, PluginPanelType, PluginView, PluginContext};

use floem::View;

// async_trait rimosso - non più necessario
use chrono::{DateTime, Utc};
use log;
use serde::{Deserialize, Serialize};

/// Errore del plugin Agent Console
#[derive(Debug, thiserror::Error)]
pub enum AgentConsoleError {
    #[error("Errore di core: {0}")]
    CoreError(#[from] CoreError),

    #[error("Errore di inizializzazione: {0}")]
    Initialization(String),

    #[error("Errore di comunicazione: {0}")]
    Communication(String),

    #[error("Errore generico: {0}")]
    Generic(String),
}

/// Entry di log dell'agente
#[derive(<PERSON>lone, Debug, Serialize, Deserialize)]
struct LogEntry {
    /// Timestamp dell'entry
    timestamp: DateTime<Utc>,

    /// Livello di log
    level: LogLevel,

    /// Messaggio
    message: String,

    /// Sorgente del messaggio (utente o agente)
    source: LogSource,
}

/// Sorgente di un messaggio di log
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq)]
enum LogSource {
    /// Messaggio dall'utente
    User,

    /// Messaggio dall'agente
    Agent,

    /// Messaggio di sistema
    System,
}

/// Vista della console dell'agente
/**
 * Vista della console dell'agente.
 * Tutte le dipendenze da tipi/conversioni/metodi di backend grafico (es. floem, vello, vger, skia)
 * sono rimosse. Si usano solo i trait pubblici di matrix-graphics-api.
 * La gestione input/eventi è tramite callback astratte.
 */
pub struct AgentConsoleView {
    logs: VecDeque<LogEntry>,
    input: String,
    plugin_id: String,
    on_command: Option<Box<dyn Fn(&str) + Send + Sync>>,
    event_bus: Option<Arc<matrix_core::EventBus>>,
}

impl AgentConsoleView {
    /// Crea una nuova vista della console dell'agente
    fn new(plugin_id: String) -> Self {
        Self {
            logs: VecDeque::new(),
            input: String::new(),
            plugin_id,
            on_command: None,
            event_bus: None,
        }
    }

    /// Imposta l'event bus
    fn set_event_bus(&mut self, event_bus: Arc<matrix_core::EventBus>) {
        self.event_bus = Some(event_bus);
    }

    /// Aggiunge un log alla console
    fn add_log(&mut self, message: &str, level: LogLevel, source: LogSource) {
        let entry = LogEntry {
            timestamp: Utc::now(),
            level,
            message: message.to_string(),
            source,
        };

        self.logs.push_back(entry);

        // Limita il numero di log
        if self.logs.len() > 1000 {
            self.logs.pop_front();
        }
    }

    /// Invia un comando all'agente
    fn send_command(&mut self) {
        if self.input.trim().is_empty() {
            return;
        }

        // Aggiungi il comando ai log
        self.add_log(&self.input.clone(), LogLevel::Info, LogSource::User);

        // Invia il comando all'agente tramite EventBus
        if let Some(event_bus) = &self.event_bus {
            // Crea un evento personalizzato con il comando
            match matrix_core::Event::custom(
                &self.plugin_id,
                "agent_command",
                &serde_json::json!({
                    "command": self.input.clone(),
                    "timestamp": chrono::Utc::now().timestamp(),
                }),
            ) {
                Ok(event) => {
                    if let Err(e) = event_bus.emit(event) {
                        self.add_log(
                            &format!("Errore nell'invio del comando: {}", e),
                            LogLevel::Error,
                            LogSource::System,
                        );
                    }
                }
                Err(e) => {
                    self.add_log(
                        &format!("Errore nella creazione dell'evento: {}", e),
                        LogLevel::Error,
                        LogSource::System,
                    );
                }
            }
        } else {
            self.add_log(
                "Event bus non disponibile",
                LogLevel::Error,
                LogSource::System,
            );
        }

        // Resetta l'input
        self.input.clear();
    }
}

/**
 * Implementazione del trait PluginView per AgentConsoleView.
 * Tutto il rendering avviene tramite i trait pubblici di matrix-graphics-api.
 * Non sono usati tipi/conversioni/metodi di backend grafico.
 */
impl PluginView for AgentConsoleView {
    fn render(&self, renderer: &mut dyn std::any::Any) {
        // Placeholder per rendering - da implementare con il sistema grafico
        println!("Rendering Agent Console with {} log entries", self.logs.len());
    }
    fn get_panel_type(&self) -> PluginPanelType {
        PluginPanelType::Right
    }
    fn get_title(&self) -> String {
        "Agent Console".to_string()
    }
    fn get_id(&self) -> String {
        format!("agent-console-view-{}", self.plugin_id)
    }
}

impl Clone for AgentConsoleView {
    fn clone(&self) -> Self {
        Self {
            logs: self.logs.clone(),
            input: self.input.clone(),
            plugin_id: self.plugin_id.clone(),
            on_command: None, // Non possiamo clonare le closure
            event_bus: self.event_bus.clone(),
        }
    }
}

/// Plugin Agent Console
pub struct AgentConsolePlugin {
    /// Info sul plugin
    info: PluginInfo,

    /// Vista della console
    view: Option<Arc<Mutex<AgentConsoleView>>>,

    /// Event bus
    event_bus: Option<Arc<matrix_core::EventBus>>,

    /// File di log per la persistenza (opzionale)
    log_file: Option<std::path::PathBuf>,

    /// Writer per il file di log
    _log_writer: Option<std::io::BufWriter<std::fs::File>>,
}

impl AgentConsolePlugin {
    /// Crea un nuovo plugin Agent Console
    pub fn new() -> Self {
        Self {
            info: PluginInfo {
                id: "matrix-agent-console".to_string(),
                name: "Agent Console".to_string(),
                version: env!("CARGO_PKG_VERSION").to_string(),
                description: "Console interattiva per l'agente AI".to_string(),
                author: "MATRIX Team".to_string(),
                dependencies: Vec::new(),
            },
            view: None,
            event_bus: None,
            log_file: None,
            _log_writer: None,
        }
    }

    /// Crea un nuovo plugin Agent Console con file di log
    pub fn with_log_file<P: Into<std::path::PathBuf>>(log_file: P) -> Result<Self, std::io::Error> {
        let log_path = log_file.into();

        // Crea le directory necessarie
        if let Some(parent) = log_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        // Apri il file di log in modalità append
        let file = std::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(&log_path)?;

        let writer = std::io::BufWriter::new(file);

        Ok(Self {
            info: PluginInfo {
                id: "matrix-agent-console".to_string(),
                name: "Agent Console".to_string(),
                version: env!("CARGO_PKG_VERSION").to_string(),
                description: "Console interattiva per l'agente AI".to_string(),
                author: "MATRIX Team".to_string(),
                dependencies: Vec::new(),
            },
            view: None,
            event_bus: None,
            log_file: Some(log_path),
            _log_writer: Some(writer),
        })
    }

    /// Scrive un log nel file di log se configurato
    fn write_to_log_file(&self, entry: &LogEntry) -> Result<(), std::io::Error> {
        if let (Some(log_path), Some(writer)) = (&self.log_file, &self._log_writer) {
            let timestamp = entry.timestamp.format("%Y-%m-%d %H:%M:%S%.3f").to_string();
            let level = format!("{:?}", entry.level);
            let source = format!("{:?}", entry.source);
            let log_line = format!(
                "[{}] [{}] [{}] {}
",
                timestamp, level, source, entry.message
            );

            // In un'implementazione reale, useremmo un writer mutex-protected
            // Per semplicità, riapriamo il file ogni volta
            let mut file = std::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(log_path)?;

            // Scrivi la linea di log
            use std::io::Write;
            file.write_all(log_line.as_bytes())?;
            file.flush()?;

            Ok(())
        } else {
            // Nessun file di log configurato
            Ok(())
        }
    }
}

/**
 * Implementazione aggiornata del trait Plugin per AgentConsolePlugin.
 * Solo metodi richiesti dalla nuova API, nessun riferimento a metodi legacy.
 */
#[async_trait::async_trait]
impl Plugin for AgentConsolePlugin {
    fn info(&self) -> PluginInfo {
        self.info.clone()
    }

    async fn initialize(&self, _context: std::sync::Arc<PluginContext>) -> Result<(), CoreError> {
        log::info!("Inizializzazione del plugin Agent Console");
        Ok(())
    }

    async fn start(&self) -> Result<(), CoreError> {
        log::info!("Avvio del plugin Agent Console");
        Ok(())
    }

    async fn stop(&self) -> Result<(), CoreError> {
        log::info!("Arresto del plugin Agent Console");
        Ok(())
    }

    async fn deinitialize(&self) -> Result<(), CoreError> {
        log::info!("Deinitializzazione del plugin Agent Console");
        Ok(())
    }

    fn create_view(&self) -> Option<Box<dyn PluginView>> {
        // Crea una nuova vista per il plugin
        let view = AgentConsoleView::new(self.info.id.clone());
        Some(Box::new(view))
    }

    fn handle_ui_event(&self, event: &Event) -> Result<(), CoreError> {
        match event {
            // Gestisci gli eventi AgentLog
            Event::AgentLog {
                level,
                message,
                context,
                timestamp,
            } => {
                // Crea un LogEntry
                let entry = LogEntry {
                    timestamp: chrono::DateTime::from_timestamp(*timestamp as i64, 0)
                        .unwrap_or_else(|| Utc::now()),
                    level: *level,
                    message: message.clone(),
                    source: LogSource::Agent,
                };

                // Scrivi nel file di log
                if let Err(e) = self.write_to_log_file(&entry) {
                    log::error!("Errore nella scrittura nel file di log: {}", e);
                }

                // Aggiungi alla vista
                if let Some(view_arc) = &self.view {
                    if let Ok(mut view) = view_arc.lock() {
                        view.add_log(message, *level, LogSource::Agent);
                    }
                }
            }

            // Gestisci gli eventi Custom
            Event::Custom { source, name, data } => {
                if name == "agent_response" {
                    // Estrai il messaggio di risposta
                    if let Some(message) = data.get("message").and_then(|m| m.as_str()) {
                        // Crea un LogEntry
                        let entry = LogEntry {
                            timestamp: Utc::now(),
                            level: LogLevel::Info,
                            message: message.to_string(),
                            source: LogSource::Agent,
                        };

                        // Scrivi nel file di log
                        if let Err(e) = self.write_to_log_file(&entry) {
                            log::error!("Errore nella scrittura nel file di log: {}", e);
                        }

                        // Aggiungi alla vista
                        if let Some(view_arc) = &self.view {
                            if let Ok(mut view) = view_arc.lock() {
                                view.add_log(message, LogLevel::Info, LogSource::Agent);
                            }
                        }
                    }
                }
            }

            _ => {}
        }

        Ok(())
    }
}

// Factory function per creare il plugin
#[no_mangle]
pub extern "C" fn create_plugin() -> *mut dyn Plugin {
    Box::into_raw(Box::new(AgentConsolePlugin::new()))
}
