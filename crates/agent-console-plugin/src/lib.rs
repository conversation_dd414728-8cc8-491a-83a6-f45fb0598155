//! Plugin Agent Console per MATRIX IDE
//!
//! Questo plugin fornisce una console per visualizzare i log dell'agente AI
//! e inviare comandi all'agente, usando solo i trait pubblici di matrix-graphics-api.

use std::collections::VecDeque;
use matrix_core::{CoreError, Event, LogLevel, Plugin, PluginPanelType, PluginView, PluginContext, PluginInfo, PluginManager};

/**
 * Tutte le dipendenze da tipi/conversioni/metodi di backend grafico (es. floem, vello, vger, skia)
 * sono state rimosse. Il plugin ora usa solo i trait pubblici di matrix-graphics-api.
 * La logica di rendering, input e gestione eventi è completamente astratta e backend-agnostica.
 * Compatibile con la nuova architettura plugin di MATRIX IDE.
 */

/// Rappresenta un messaggio di log dell'agente
#[derive(<PERSON><PERSON>, Debug, serde::Serialize, serde::Deserialize)]
struct LogEntry {
    /// Timestamp del messaggio
    timestamp: chrono::DateTime<chrono::Utc>,
    /// Livello del messaggio
    level: LogLevel,
    /// Testo del messaggio
    message: String,
    /// Fonte del messaggio (utente o agente)
    source: MessageSource,
}

// Usiamo LogLevel da matrix_core invece di definirne uno locale

/// Fonte del messaggio
#[derive(Clone, Debug, serde::Serialize, serde::Deserialize)]
enum MessageSource {
    User,
    Agent,
    System,
}

/**
 * Vista della console dell'agente.
 * Non dipende da tipi concreti di backend grafico.
 * Il rendering avviene tramite il trait MatrixRenderer.
 * La gestione dell'input e degli eventi è demandata a callback astratte fornite dall'host.
 * Tutte le conversioni e pattern matching sono compatibili solo con i trait pubblici.
 */
pub struct AgentConsoleView {
    /// Log dell'agente
    logs: VecDeque<LogEntry>,
    /// Input corrente
    input: String,
    /// Callback per invio comando
    on_command: Option<Box<dyn Fn(&str) + Send + Sync>>,
}

impl Clone for AgentConsoleView {
    fn clone(&self) -> Self {
        Self {
            logs: self.logs.clone(),
            input: self.input.clone(),
            on_command: None, // Le closure non possono essere clonate
        }
    }
}

impl AgentConsoleView {
    pub fn new() -> Self {
        Self {
            logs: VecDeque::new(),
            input: String::new(),
            on_command: None,
        }
    }

    /// Imposta la callback per l'invio dei comandi.
    /// L'host deve fornire una closure che implementa la logica di invio comando.
    pub fn set_on_command<F>(&mut self, cb: F)
    where
        F: Fn(&str) + Send + Sync + 'static,
    {
        self.on_command = Some(Box::new(cb));
    }

    pub fn add_log(&mut self, message: &str, level: LogLevel, source: MessageSource) {
        let entry = LogEntry {
            timestamp: chrono::Utc::now(),
            level,
            message: message.to_string(),
            source,
        };
        self.logs.push_back(entry);
        while self.logs.len() > 100 {
            self.logs.pop_front();
        }
    }

    pub fn set_input(&mut self, value: &str) {
        self.input = value.to_string();
    }

    /// Invia il comando corrente tramite la callback astratta.
    /// Non ci sono più riferimenti a widget o segnali reattivi.
    pub fn send_message(&mut self) {
        if let Some(cb) = &self.on_command {
            cb(&self.input);
        }
        self.input.clear();
    }

    /// Renderizza la console tramite il trait MatrixRenderer.
    /// Tutto il disegno avviene tramite le primitive pubbliche dell'API.
    /// Non sono usati pattern, conversioni o tipi non compatibili con i trait pubblici.
    pub fn render(&self, renderer: &mut dyn matrix_graphics_api::MatrixRenderer) {
        use matrix_graphics_api::{MatrixBrush, MatrixColor};
        // Sfondo della console
        let bg = SimpleColor::new(0.12, 0.12, 0.12, 1.0);
        let bg_brush = SimpleBrush::new(bg);
        renderer.set_brush(&bg_brush);
        renderer.clear();

        // Disegno dei messaggi di log
        let mut y = 10.0;
        for entry in &self.logs {
            // Pattern matching solo su enum pubblici.
            let color = match (entry.level, entry.source) {
                (LogLevel::Error, _) => SimpleColor::new(1.0, 0.0, 0.0, 1.0),
                (LogLevel::Warning, _) => SimpleColor::new(1.0, 0.65, 0.0, 1.0),
                (_, MessageSource::User) => SimpleColor::new(0.0, 0.5, 1.0, 1.0),
                (_, MessageSource::Agent) => SimpleColor::new(0.0, 0.78, 0.0, 1.0),
                _ => SimpleColor::new(0.7, 0.7, 0.7, 1.0),
            };
            let brush = SimpleBrush::new(color);
            renderer.set_brush(&brush);
            renderer.draw_rect(10.0, y, 480.0, 20.0);

            // Utilizziamo il metodo draw_text per visualizzare il messaggio
            let text_color = SimpleColor::new(0.9, 0.9, 0.9, 1.0);
            let text_brush = SimpleBrush::new(text_color);
            renderer.set_brush(&text_brush);
            renderer.draw_text(&entry.message, 20.0, y + 15.0);
            y += 24.0;
        }

        // Disegno dell'input box
        let input_bg = SimpleColor::new(0.2, 0.2, 0.2, 1.0);
        let input_brush = SimpleBrush::new(input_bg);
        renderer.set_brush(&input_brush);
        let input_y = y + 10.0;
        renderer.draw_rect(10.0, input_y, 400.0, 30.0);

        // Disegno del testo di input
        let input_text_color = SimpleColor::new(1.0, 1.0, 1.0, 1.0);
        let input_text_brush = SimpleBrush::new(input_text_color);
        renderer.set_brush(&input_text_brush);
        renderer.draw_text(&self.input, 15.0, input_y + 20.0);

        // Disegno del pulsante "Invia"
        let button_color = SimpleColor::new(0.2, 0.5, 0.8, 1.0);
        let button_brush = SimpleBrush::new(button_color);
        renderer.set_brush(&button_brush);
        renderer.draw_rect(420.0, input_y, 70.0, 30.0);

        // Testo del pulsante
        let button_text_color = SimpleColor::new(1.0, 1.0, 1.0, 1.0);
        let button_text_brush = SimpleBrush::new(button_text_color);
        renderer.set_brush(&button_text_brush);
        renderer.draw_text("Invia", 435.0, input_y + 20.0);
    }
}

/// Implementazione semplice di MatrixColor per i rendering base.
struct SimpleColor {
    r: f32,
    g: f32,
    b: f32,
    a: f32,
}
impl SimpleColor {
    pub fn new(r: f32, g: f32, b: f32, a: f32) -> Self {
        Self { r, g, b, a }
    }
}
impl matrix_graphics_api::MatrixColor for SimpleColor {
    fn red(&self) -> f32 { self.r }
    fn green(&self) -> f32 { self.g }
    fn blue(&self) -> f32 { self.b }
    fn alpha(&self) -> f32 { self.a }
}

/// Implementazione semplice di MatrixBrush che usa un solo colore.
struct SimpleBrush {
    color: SimpleColor,
}
impl SimpleBrush {
    pub fn new(color: SimpleColor) -> Self {
        Self { color }
    }
}
impl matrix_graphics_api::MatrixBrush for SimpleBrush {
    fn color(&self) -> Option<&dyn matrix_graphics_api::MatrixColor> {
        Some(&self.color)
    }
    fn opacity(&self) -> f32 {
        self.color.a
    }
}

/// Implementazione del trait PluginView per AgentConsoleView.
impl PluginView for AgentConsoleView {
    fn render(&self, _renderer: &mut dyn std::any::Any) {
        // Rendering placeholder - da implementare con il sistema grafico
    }
    fn get_panel_type(&self) -> PluginPanelType {
        PluginPanelType::Right
    }
    fn get_title(&self) -> String {
        "Agent Console".to_string()
    }
    fn get_id(&self) -> String {
        "agent-console-view".to_string()
    }
}

/**
 * Plugin Agent Console.
 * Non mantiene stato reattivo né tipi grafici concreti.
 * Gestisce solo dati e logica, delegando il rendering e l’input alle API astratte.
 * Tutte le chiamate e conversioni sono compatibili con la boundary API pubblica.
 * Nessun workaround temporaneo: ogni fix è reale e tracciato nei commenti.
 */
pub struct AgentConsolePlugin {
    id: String,
    name: String,
    description: String,
    view: AgentConsoleView,
}

impl AgentConsolePlugin {
    pub fn new() -> Self {
        let mut view = AgentConsoleView::new();
        view.set_on_command(|cmd| {
            println!("Comando inviato: {}", cmd);
        });
        Self {
            id: "agent-console".to_string(),
            name: "Agent Console".to_string(),
            description: "Console per interagire con l'agente AI di MATRIX IDE".to_string(),
            view,
        }
    }
}

#[async_trait::async_trait]
impl Plugin for AgentConsolePlugin {
    fn info(&self) -> PluginInfo {
        PluginInfo {
            id: self.id.clone(),
            name: self.name.clone(),
            version: "0.1.0".to_string(),
            description: self.description.clone(),
            author: "MATRIX Team".to_string(),
            dependencies: vec![],
        }
    }
    async fn initialize(&self, _context: std::sync::Arc<PluginContext>) -> Result<(), CoreError> {
        Ok(())
    }
    async fn start(&self) -> Result<(), CoreError> {
        Ok(())
    }
    async fn stop(&self) -> Result<(), CoreError> {
        Ok(())
    }
    async fn deinitialize(&self) -> Result<(), CoreError> {
        Ok(())
    }
    fn create_view(&self) -> Option<Box<dyn PluginView>> {
        Some(Box::new(self.view.clone()))
    }
    fn handle_ui_event(&self, event: &Event) -> Result<(), CoreError> {
        if let Event::Custom { name, data, .. } = event {
            if name == "agent_response" {
                if let Ok(response) = serde_json::from_value::<String>(data.clone()) {
                    // Aggiungiamo log all'agente usando evento di sistema
                    // Questo è un pattern di comunicazione più robusto rispetto alla modifica diretta
                    if let Some(context) = PluginManager::global().get_plugin_context(&self.id) {
                        let _ = context.emit_custom("agent_log_added", &LogEntry {
                            timestamp: chrono::Utc::now(),
                            level: LogLevel::Info,
                            message: response,
                            source: MessageSource::Agent,
                        });
                    }
                }
            } else if name == "agent_log_added" {
                // In un contesto reale, questo evento sarebbe gestito altrove per aggiornare la UI
                log::info!("Ricevuto log dell'agente: {:?}", data);
            }
        }
        Ok(())
    }
}
