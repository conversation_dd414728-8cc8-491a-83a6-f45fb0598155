//! Plugin Agent Console per MATRIX IDE
//!
//! Questo plugin fornisce una console per visualizzare i log dell'agente AI
//! e inviare comandi all'agente, usando solo i trait pubblici di matrix-graphics-api.

use std::collections::VecDeque;
use matrix_core::{CoreError, Event, LogLevel, Plugin, PluginPanelType, PluginView, PluginContext, PluginInfo, PluginManager};

/**
 * <PERSON>tte le dipendenze da tipi/conversioni/metodi di backend grafico (es. floem, vello, vger, skia)
 * sono state rimosse. Il plugin ora usa solo i trait pubblici di matrix-graphics-api.
 * La logica di rendering, input e gestione eventi è completamente astratta e backend-agnostica.
 * Compatibile con la nuova architettura plugin di MATRIX IDE.
 */

/// Rappresenta un messaggio di log dell'agente
#[derive(<PERSON><PERSON>, Debug, serde::Serialize, serde::Deserialize)]
struct LogEntry {
    /// Timestamp del messaggio
    timestamp: chrono::DateTime<chrono::Utc>,
    /// Livello del messaggio
    level: LogLevel,
    /// Testo del messaggio
    message: String,
    /// Fonte del messaggio (utente o agente)
    source: MessageSource,
}

// Usiamo LogLevel da matrix_core invece di definirne uno locale

/// Fonte del messaggio
#[derive(Clone, Copy, Debug, serde::Serialize, serde::Deserialize)]
enum MessageSource {
    User,
    Agent,
    System,
}

/**
 * Vista della console dell'agente.
 * Non dipende da tipi concreti di backend grafico.
 * Il rendering avviene tramite il trait MatrixRenderer.
 * La gestione dell'input e degli eventi è demandata a callback astratte fornite dall'host.
 * Tutte le conversioni e pattern matching sono compatibili solo con i trait pubblici.
 */
pub struct AgentConsoleView {
    /// Log dell'agente
    logs: VecDeque<LogEntry>,
    /// Input corrente
    input: String,
    /// Callback per invio comando
    on_command: Option<Box<dyn Fn(&str) + Send + Sync>>,
}

impl Clone for AgentConsoleView {
    fn clone(&self) -> Self {
        Self {
            logs: self.logs.clone(),
            input: self.input.clone(),
            on_command: None, // Le closure non possono essere clonate
        }
    }
}

impl AgentConsoleView {
    pub fn new() -> Self {
        Self {
            logs: VecDeque::new(),
            input: String::new(),
            on_command: None,
        }
    }

    /// Imposta la callback per l'invio dei comandi.
    /// L'host deve fornire una closure che implementa la logica di invio comando.
    pub fn set_on_command<F>(&mut self, cb: F)
    where
        F: Fn(&str) + Send + Sync + 'static,
    {
        self.on_command = Some(Box::new(cb));
    }

    pub fn add_log(&mut self, message: &str, level: LogLevel, source: MessageSource) {
        let entry = LogEntry {
            timestamp: chrono::Utc::now(),
            level,
            message: message.to_string(),
            source,
        };
        self.logs.push_back(entry);
        while self.logs.len() > 100 {
            self.logs.pop_front();
        }
    }

    pub fn set_input(&mut self, value: &str) {
        self.input = value.to_string();
    }

    /// Invia il comando corrente tramite la callback astratta.
    /// Non ci sono più riferimenti a widget o segnali reattivi.
    pub fn send_message(&mut self) {
        if let Some(cb) = &self.on_command {
            cb(&self.input);
        }
        self.input.clear();
    }

    /// Renderizza la console (placeholder per implementazione futura)
    pub fn render(&self, _renderer: &mut dyn std::any::Any) {
        // Placeholder per rendering - da implementare con il sistema grafico
        println!("Rendering console with {} log entries", self.logs.len());
    }
}

/// Implementazione semplice di MatrixColor per i rendering base.
struct SimpleColor {
    r: f32,
    g: f32,
    b: f32,
    a: f32,
}
impl SimpleColor {
    pub fn new(r: f32, g: f32, b: f32, a: f32) -> Self {
        Self { r, g, b, a }
    }
}
impl matrix_graphics_api::MatrixColor for SimpleColor {
    fn red(&self) -> f32 { self.r }
    fn green(&self) -> f32 { self.g }
    fn blue(&self) -> f32 { self.b }
    fn alpha(&self) -> f32 { self.a }
}

/// Implementazione semplice di MatrixBrush che usa un solo colore.
struct SimpleBrush {
    color: SimpleColor,
}
impl SimpleBrush {
    pub fn new(color: SimpleColor) -> Self {
        Self { color }
    }
}
impl matrix_graphics_api::MatrixBrush for SimpleBrush {
    fn color(&self) -> Option<&dyn matrix_graphics_api::MatrixColor> {
        Some(&self.color)
    }
    fn opacity(&self) -> f32 {
        self.color.a
    }
}

/// Implementazione del trait PluginView per AgentConsoleView.
impl PluginView for AgentConsoleView {
    fn render(&self, _renderer: &mut dyn std::any::Any) {
        // Rendering placeholder - da implementare con il sistema grafico
    }
    fn get_panel_type(&self) -> PluginPanelType {
        PluginPanelType::Right
    }
    fn get_title(&self) -> String {
        "Agent Console".to_string()
    }
    fn get_id(&self) -> String {
        "agent-console-view".to_string()
    }
}

/**
 * Plugin Agent Console.
 * Non mantiene stato reattivo né tipi grafici concreti.
 * Gestisce solo dati e logica, delegando il rendering e l’input alle API astratte.
 * Tutte le chiamate e conversioni sono compatibili con la boundary API pubblica.
 * Nessun workaround temporaneo: ogni fix è reale e tracciato nei commenti.
 */
pub struct AgentConsolePlugin {
    id: String,
    name: String,
    description: String,
    view: AgentConsoleView,
}

impl AgentConsolePlugin {
    pub fn new() -> Self {
        let mut view = AgentConsoleView::new();
        view.set_on_command(|cmd| {
            println!("Comando inviato: {}", cmd);
        });
        Self {
            id: "agent-console".to_string(),
            name: "Agent Console".to_string(),
            description: "Console per interagire con l'agente AI di MATRIX IDE".to_string(),
            view,
        }
    }
}

#[async_trait::async_trait]
impl Plugin for AgentConsolePlugin {
    fn info(&self) -> PluginInfo {
        PluginInfo {
            id: self.id.clone(),
            name: self.name.clone(),
            version: "0.1.0".to_string(),
            description: self.description.clone(),
            author: "MATRIX Team".to_string(),
            dependencies: vec![],
        }
    }
    async fn initialize(&self, _context: std::sync::Arc<PluginContext>) -> Result<(), CoreError> {
        Ok(())
    }
    async fn start(&self) -> Result<(), CoreError> {
        Ok(())
    }
    async fn stop(&self) -> Result<(), CoreError> {
        Ok(())
    }
    async fn deinitialize(&self) -> Result<(), CoreError> {
        Ok(())
    }
    fn create_view(&self) -> Option<Box<dyn PluginView>> {
        Some(Box::new(self.view.clone()))
    }
    fn handle_ui_event(&self, event: &Event) -> Result<(), CoreError> {
        if let Event::Custom { name, data, .. } = event {
            if name == "agent_response" {
                if let Ok(response) = serde_json::from_value::<String>(data.clone()) {
                    // Aggiungiamo log all'agente usando evento di sistema
                    // Questo è un pattern di comunicazione più robusto rispetto alla modifica diretta
                    if let Some(context) = PluginManager::global().get_plugin_context(&self.id) {
                        let _ = context.emit_custom("agent_log_added", &LogEntry {
                            timestamp: chrono::Utc::now(),
                            level: LogLevel::Info,
                            message: response,
                            source: MessageSource::Agent,
                        });
                    }
                }
            } else if name == "agent_log_added" {
                // In un contesto reale, questo evento sarebbe gestito altrove per aggiornare la UI
                log::info!("Ricevuto log dell'agente: {:?}", data);
            }
        }
        Ok(())
    }
}
