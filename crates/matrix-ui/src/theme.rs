//! Gestione dei temi per MATRIX IDE
//!
//! Questo modulo implementa un sistema di temi personalizzabile,
//! che permette di cambiare l'aspetto dell'interfaccia utente.

use std::collections::HashMap;
use std::sync::{RwLock};
use std::path::PathBuf;
use serde::{Serialize, Deserialize};
use floem::peniko::Color;
use crate::error::UiError;

/// Colori del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeColors {
    /// Colore di sfondo principale
    pub background: Color,

    /// Colore di sfondo secondario
    pub background_secondary: Color,

    /// Colore di sfondo terziario
    pub background_tertiary: Color,

    /// Colore del testo principale
    pub text: Color,

    /// Colore del testo secondario
    pub text_secondary: Color,

    /// Colore del testo terziario
    pub text_tertiary: Color,

    /// Colore primario dell'accento
    pub accent: Color,

    /// Colore secondario dell'accento
    pub accent_secondary: Color,

    /// Colore del bordo
    pub border: Color,

    /// Colore dell'errore
    pub error: Color,

    /// Colore di avviso
    pub warning: Color,

    /// Colore di successo
    pub success: Color,

    /// Colore di informazione
    pub info: Color,

    /// Colore primario (per logo e accenti)
    pub primary: Color,

    /// Colore della superficie (per pannelli e componenti)
    pub surface: Color,

    /// Colore di hover per elementi interattivi
    pub hover: Color,

    /// Colore trasparente
    pub transparent: Color,

    /// Colore active (per elementi attivi)
    pub active: Color,

    /// Colore di pericolo (per azioni distruttive)
    pub danger: Color,

    /// Colore di pericolo attivo
    pub danger_active: Color,
}

/// Colori per syntax highlighting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyntaxColors {
    /// Colore per le parole chiave
    pub keyword: Color,

    /// Colore per le stringhe
    pub string: Color,

    /// Colore per i numeri
    pub number: Color,

    /// Colore per i commenti
    pub comment: Color,

    /// Colore per gli identificatori
    pub identifier: Color,

    /// Colore per i nomi di funzione
    pub function: Color,

    /// Colore per i nomi di tipo
    pub type_name: Color,

    /// Colore per gli operatori
    pub operator: Color,

    /// Colore per la punteggiatura
    pub punctuation: Color,

    /// Colore per le direttive preprocessore
    pub preprocessor: Color,

    /// Colore per le costanti
    pub constant: Color,

    /// Colore per i built-in
    pub builtin: Color,

    /// Colore per gli errori
    pub error: Color,
}

impl Default for SyntaxColors {
    fn default() -> Self {
        // Colori ispirati ai migliori temi per syntax highlighting
        Self {
            keyword: Color::rgb8(86, 156, 214),        // Blue - Keywords (if, else, fn)
            string: Color::rgb8(206, 145, 120),        // Orange - String literals
            number: Color::rgb8(181, 206, 168),        // Light green - Numbers
            comment: Color::rgb8(106, 153, 85),        // Green - Comments
            identifier: Color::rgb8(220, 220, 170),    // Light yellow - Variables
            function: Color::rgb8(220, 220, 170),      // Light yellow - Function names
            type_name: Color::rgb8(78, 201, 176),      // Cyan - Type names
            operator: Color::rgb8(212, 212, 212),      // Light gray - Operators
            punctuation: Color::rgb8(212, 212, 212),   // Light gray - Punctuation
            preprocessor: Color::rgb8(155, 155, 155),  // Gray - Preprocessor
            constant: Color::rgb8(79, 193, 255),       // Light blue - Constants
            builtin: Color::rgb8(197, 134, 192),       // Purple - Built-ins
            error: Color::rgb8(244, 71, 71),           // Red - Errors
        }
    }
}

impl Default for ThemeColors {
    fn default() -> Self {
        // === MATRIX DARK THEME - Palette Professionale ===
        // Ispirato ai migliori IDE moderni: VS Code Dark+, JetBrains Darcula, Lapce Dark
        Self {
            // === BACKGROUNDS - Scala di grigi elegante ===
            background: Color::rgb8(24, 24, 27),              // Zinc-900 - Background principale
            background_secondary: Color::rgb8(39, 39, 42),    // Zinc-800 - Pannelli secondari
            background_tertiary: Color::rgb8(63, 63, 70),     // Zinc-700 - Elementi elevati

            // === TEXT - Contrasto ottimale per leggibilità ===
            text: Color::rgb8(250, 250, 250),                 // Zinc-50 - Testo principale
            text_secondary: Color::rgb8(161, 161, 170),       // Zinc-400 - Testo secondario
            text_tertiary: Color::rgb8(113, 113, 122),        // Zinc-500 - Testo disabilitato

            // === ACCENTS - Matrix signature colors ===
            accent: Color::rgb8(34, 197, 94),                 // Green-500 - Matrix green
            accent_secondary: Color::rgb8(22, 163, 74),       // Green-600 - Matrix green dark
            primary: Color::rgb8(59, 130, 246),               // Blue-500 - Primary actions

            // === INTERFACE ELEMENTS ===
            surface: Color::rgb8(39, 39, 42),                 // Zinc-800 - Cards, panels
            border: Color::rgb8(82, 82, 91),                  // Zinc-600 - Subtle borders
            hover: Color::rgba8(255, 255, 255, 8),            // White 3% - Hover overlay
            active: Color::rgba8(255, 255, 255, 16),          // White 6% - Active state
            transparent: Color::TRANSPARENT,                   // Transparent

            // === STATUS COLORS - Semantic and accessible ===
            success: Color::rgb8(34, 197, 94),                // Green-500 - Success
            warning: Color::rgb8(245, 158, 11),               // Amber-500 - Warning
            error: Color::rgb8(239, 68, 68),                  // Red-500 - Error
            info: Color::rgb8(59, 130, 246),                  // Blue-500 - Info
            danger: Color::rgb8(239, 68, 68),                 // Red-500 - Danger
            danger_active: Color::rgb8(220, 38, 38),          // Red-600 - Danger active
        }
    }
}

/// Spaziatura del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeSpacing {
    /// Spaziatura molto piccola
    pub xxs: f32,

    /// Spaziatura piccola
    pub xs: f32,

    /// Spaziatura media
    pub sm: f32,

    /// Spaziatura normale
    pub md: f32,

    /// Spaziatura grande
    pub lg: f32,

    /// Spaziatura molto grande
    pub xl: f32,

    /// Spaziatura extra grande
    pub xxl: f32,
}

impl Default for ThemeSpacing {
    fn default() -> Self {
        // PROFESSIONAL SPACING SYSTEM - 4px base unit
        // Follows 8-point grid system for consistency
        Self {
            xxs: 2.0,      // 0.5 units - Micro spacing
            xs: 4.0,       // 1 unit - Minimal spacing
            sm: 8.0,       // 2 units - Small spacing
            md: 16.0,      // 4 units - Standard spacing
            lg: 24.0,      // 6 units - Large spacing
            xl: 32.0,      // 8 units - Extra large spacing
            xxl: 48.0,     // 12 units - Maximum spacing
        }
    }
}

/// Dimensioni del testo del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeFontSizes {
    /// Dimensione del testo molto piccola
    pub xxs: f32,

    /// Dimensione del testo piccola
    pub xs: f32,

    /// Dimensione del testo media
    pub sm: f32,

    /// Dimensione del testo normale
    pub md: f32,

    /// Dimensione del testo grande
    pub lg: f32,

    /// Dimensione del testo molto grande
    pub xl: f32,

    /// Dimensione del testo extra grande
    pub xxl: f32,
}

impl Default for ThemeFontSizes {
    fn default() -> Self {
        // PROFESSIONAL TYPOGRAPHY SCALE
        // Optimized for code editors and IDE interfaces
        Self {
            xxs: 10.0,     // Captions, metadata
            xs: 11.0,      // Secondary text, labels
            sm: 12.0,      // Small UI text
            md: 13.0,      // Body text, default (IDE standard)
            lg: 14.0,      // Headings, emphasis
            xl: 16.0,      // Large headings
            xxl: 18.0,     // Display text
        }
    }
}

/// Stili delle icone del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeIcons {
    /// Dimensione dell'icona piccola
    pub size_small: f32,

    /// Dimensione dell'icona media
    pub size_medium: f32,

    /// Dimensione dell'icona grande
    pub size_large: f32,

    /// Spessore della linea
    pub stroke_width: f32,
}

impl Default for ThemeIcons {
    fn default() -> Self {
        Self {
            size_small: 16.0,
            size_medium: 24.0,
            size_large: 32.0,
            stroke_width: 1.5,
        }
    }
}

/// Sistema di tipografia professionale
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeTypography {
    /// Font family per il codice (monospace)
    pub font_family_code: String,

    /// Font family per l'interfaccia (sans-serif)
    pub font_family_ui: String,

    /// Font family per i display (headings)
    pub font_family_display: String,

    /// Peso del font normale
    pub font_weight_normal: u16,

    /// Peso del font medio
    pub font_weight_medium: u16,

    /// Peso del font bold
    pub font_weight_bold: u16,

    /// Line height normale
    pub line_height_normal: f32,

    /// Line height compatto
    pub line_height_tight: f32,

    /// Line height rilassato
    pub line_height_relaxed: f32,
}

impl Default for ThemeTypography {
    fn default() -> Self {
        Self {
            // PROFESSIONAL FONT FAMILIES
            font_family_code: "JetBrains Mono, Fira Code, SF Mono, Monaco, Inconsolata, Roboto Mono, monospace".to_string(),
            font_family_ui: "Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif".to_string(),
            font_family_display: "Inter, system-ui, sans-serif".to_string(),

            // FONT WEIGHTS - Semantic naming
            font_weight_normal: 400,
            font_weight_medium: 500,
            font_weight_bold: 600,

            // LINE HEIGHTS - Optimized for readability
            line_height_tight: 1.25,    // Headings
            line_height_normal: 1.5,    // Body text
            line_height_relaxed: 1.75,  // Long-form content
        }
    }
}

/// Stili dei bordi del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeBorders {
    /// Raggio del bordo piccolo
    pub radius_small: f32,

    /// Raggio del bordo medio
    pub radius_medium: f32,

    /// Raggio del bordo grande
    pub radius_large: f32,

    /// Spessore del bordo sottile
    pub width_thin: f32,

    /// Spessore del bordo normale
    pub width_normal: f32,

    /// Spessore del bordo spesso
    pub width_thick: f32,
}

impl Default for ThemeBorders {
    fn default() -> Self {
        Self {
            radius_small: 2.0,
            radius_medium: 4.0,
            radius_large: 8.0,
            width_thin: 1.0,
            width_normal: 2.0,
            width_thick: 3.0,
        }
    }
}

/// Sistema di animazioni professionali
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeAnimations {
    /// Durata animazione istantanea (ms)
    pub duration_instant: u32,

    /// Durata animazione veloce (ms)
    pub duration_fast: u32,

    /// Durata animazione normale (ms)
    pub duration_normal: u32,

    /// Durata animazione lenta (ms)
    pub duration_slow: u32,

    /// Curva di easing per entrata
    pub ease_in: String,

    /// Curva di easing per uscita
    pub ease_out: String,

    /// Curva di easing per entrata e uscita
    pub ease_in_out: String,
}

impl Default for ThemeAnimations {
    fn default() -> Self {
        Self {
            // ANIMATION DURATIONS - Consistent timing
            duration_instant: 0,          // Immediate
            duration_fast: 150,           // Quick interactions
            duration_normal: 250,         // Standard transitions
            duration_slow: 350,           // Complex animations

            // EASING CURVES - Natural motion
            ease_in: "cubic-bezier(0.4, 0, 1, 1)".to_string(),
            ease_out: "cubic-bezier(0, 0, 0.2, 1)".to_string(),
            ease_in_out: "cubic-bezier(0.4, 0, 0.2, 1)".to_string(),
        }
    }
}

/// Tema completo dell'interfaccia utente
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Theme {
    /// Nome del tema
    pub name: String,

    /// Descrizione del tema
    pub description: String,

    /// Autore del tema
    pub author: String,

    /// Versione del tema
    pub version: String,

    /// Colori del tema
    pub colors: ThemeColors,

    /// Spaziatura del tema
    pub spacing: ThemeSpacing,

    /// Dimensioni del testo del tema
    pub font_sizes: ThemeFontSizes,

    /// Stili delle icone del tema
    pub icons: ThemeIcons,

    /// Stili dei bordi del tema
    pub borders: ThemeBorders,

    /// Sistema di tipografia
    pub typography: ThemeTypography,

    /// Sistema di animazioni
    pub animations: ThemeAnimations,

    /// Colori per syntax highlighting
    pub syntax: SyntaxColors,

    /// È un tema scuro?
    pub is_dark: bool,
}

impl Default for Theme {
    fn default() -> Self {
        Self {
            name: "MATRIX Professional Dark".to_string(),
            description: "Professional dark theme for MATRIX IDE with modern design standards".to_string(),
            author: "MATRIX IDE Team".to_string(),
            version: "2.0.0".to_string(),
            colors: ThemeColors::default(),
            spacing: ThemeSpacing::default(),
            font_sizes: ThemeFontSizes::default(),
            icons: ThemeIcons::default(),
            borders: ThemeBorders::default(),
            typography: ThemeTypography::default(),
            animations: ThemeAnimations::default(),
            syntax: SyntaxColors::default(),
            is_dark: true,
        }
    }
}

impl Theme {
    /// Crea il tema light professionale
    pub fn professional_light() -> Self {
        let mut theme = Self::default();
        theme.name = "MATRIX Professional Light".to_string();
        theme.description = "Professional light theme for MATRIX IDE with modern design standards".to_string();
        theme.is_dark = false;

        // MATRIX LIGHT THEME - Professional Grade
        theme.colors = ThemeColors {
            // BACKGROUNDS
            background: Color::rgb8(255, 255, 255),           // White - Main background
            background_secondary: Color::rgb8(248, 250, 252), // Slate-50 - Secondary panels
            background_tertiary: Color::rgb8(241, 245, 249),  // Slate-100 - Elevated elements

            // TEXT
            text: Color::rgb8(15, 23, 42),                    // Slate-900 - Primary text
            text_secondary: Color::rgb8(71, 85, 105),         // Slate-600 - Secondary text
            text_tertiary: Color::rgb8(148, 163, 184),        // Slate-400 - Disabled text

            // ACCENTS
            accent: Color::rgb8(22, 163, 74),                 // Green-600 - Matrix green
            accent_secondary: Color::rgb8(21, 128, 61),       // Green-700 - Matrix green dark
            primary: Color::rgb8(37, 99, 235),                // Blue-600 - Primary actions

            // INTERFACE ELEMENTS
            border: Color::rgb8(203, 213, 225),               // Slate-300 - Borders
            surface: Color::rgb8(248, 250, 252),              // Slate-50 - Cards, panels

            // INTERACTION STATES
            hover: Color::rgba8(0, 0, 0, 13),                 // Black 5% - Hover overlay
            active: Color::rgba8(0, 0, 0, 26),                // Black 10% - Active state

            // STATUS COLORS
            success: Color::rgb8(22, 163, 74),                // Green-600 - Success
            warning: Color::rgb8(217, 119, 6),                // Amber-600 - Warning
            error: Color::rgb8(220, 38, 38),                  // Red-600 - Error
            info: Color::rgb8(37, 99, 235),                   // Blue-600 - Info
            danger: Color::rgb8(220, 38, 38),                 // Red-600 - Danger
            danger_active: Color::rgb8(185, 28, 28),          // Red-700 - Danger active

            // UTILITY
            transparent: Color::TRANSPARENT,
        };

        theme
    }
}

/// Gestore dei temi
#[derive(Debug)]
pub struct ThemeManager {
    /// Temi disponibili
    themes: RwLock<HashMap<String, Theme>>,

    /// Tema attualmente attivo
    active_theme: RwLock<Theme>,

    /// Directory dei temi
    themes_dir: PathBuf,
}

impl ThemeManager {
    /// Crea un nuovo gestore dei temi
    pub fn new() -> Result<Self, UiError> {
        let default_theme = Theme::default();
        let mut themes = HashMap::new();
        themes.insert(default_theme.name.clone(), default_theme.clone());

        // Aggiungi tema light
        let light_theme = Self::create_light_theme();
        themes.insert(light_theme.name.clone(), light_theme);

        // Aggiungi tema high contrast
        let high_contrast_theme = Self::create_high_contrast_theme();
        themes.insert(high_contrast_theme.name.clone(), high_contrast_theme);

        // Determina la directory dei temi
        let themes_dir = dirs::config_dir()
            .ok_or_else(|| UiError::ThemeError("Failed to determine config directory".to_string()))?
            .join("matrix-ide")
            .join("themes");

        Ok(Self {
            themes: RwLock::new(themes),
            active_theme: RwLock::new(default_theme),
            themes_dir,
        })
    }

    /// Inizializza il gestore dei temi
    pub fn initialize(&self) -> Result<(), UiError> {
        // Crea la directory dei temi se non esiste
        std::fs::create_dir_all(&self.themes_dir)?;

        // Carica i temi dalla directory
        self.load_themes()?;

        Ok(())
    }

    /// Carica i temi dalla directory
    pub fn load_themes(&self) -> Result<(), UiError> {
        // Se la directory non esiste ancora, la crea
        if !self.themes_dir.exists() {
            std::fs::create_dir_all(&self.themes_dir)?;
        }

        // Itera sui file nella directory
        for entry in std::fs::read_dir(&self.themes_dir)? {
            let entry = entry?;
            let path = entry.path();

            // Verifica che sia un file JSON
            if path.is_file() && path.extension().is_some_and(|ext| ext == "json") {
                // Carica il tema dal file
                let theme_json = std::fs::read_to_string(&path)?;
                match serde_json::from_str::<Theme>(&theme_json) {
                    Ok(theme) => {
                        // Aggiunge il tema alla mappa
                        let mut themes = self.themes.write().map_err(|_| {
                            UiError::LockError("Failed to acquire write lock on themes".to_string())
                        })?;

                        themes.insert(theme.name.clone(), theme);
                    }
                    Err(e) => {
                        eprintln!("Error loading theme from {}: {}", path.display(), e);
                    }
                }
            }
        }

        Ok(())
    }

    /// Ottiene il tema attualmente attivo
    pub fn get_active_theme(&self) -> Result<Theme, UiError> {
        let theme = self.active_theme.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on active theme".to_string())
        })?;

        Ok((*theme).clone())
    }

    /// Imposta il tema attivo
    pub fn set_active_theme(&self, theme_name: &str) -> Result<(), UiError> {
        let themes = self.themes.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on themes".to_string())
        })?;

        let theme = themes.get(theme_name).ok_or_else(|| {
            UiError::ThemeError(format!("Theme '{}' not found", theme_name))
        })?;

        let mut active_theme = self.active_theme.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on active theme".to_string())
        })?;

        *active_theme = theme.clone();

        Ok(())
    }

    /// Ottiene tutti i temi disponibili
    pub fn get_available_themes(&self) -> Result<Vec<String>, UiError> {
        let themes = self.themes.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on themes".to_string())
        })?;

        Ok(themes.keys().cloned().collect())
    }

    /// Aggiunge un nuovo tema
    pub fn add_theme(&self, theme: Theme) -> Result<(), UiError> {
        // Aggiunge il tema alla mappa
        {
            let mut themes = self.themes.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on themes".to_string())
            })?;

            themes.insert(theme.name.clone(), theme.clone());
        }

        // Salva il tema su disco
        self.save_theme(&theme)?;

        Ok(())
    }

    /// Salva un tema su disco
    pub fn save_theme(&self, theme: &Theme) -> Result<(), UiError> {
        // Crea la directory dei temi se non esiste
        if !self.themes_dir.exists() {
            std::fs::create_dir_all(&self.themes_dir)?;
        }

        // Serializza il tema in JSON
        let theme_json = serde_json::to_string_pretty(theme)
            .map_err(|e| UiError::ThemeError(format!("Failed to serialize theme: {}", e)))?;

        // Salva il tema su disco
        let file_path = self.themes_dir.join(format!("{}.json", theme.name));
        std::fs::write(file_path, theme_json)?;

        Ok(())
    }

    /// Arresta il gestore dei temi
    pub fn shutdown(&self) -> Result<(), UiError> {
        // Assicura che tutti i temi siano salvati su disco
        let themes = self.themes.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on themes".to_string())
        })?;

        for theme in themes.values() {
            self.save_theme(theme)?;
        }

        Ok(())
    }

    /// Crea il tema light
    fn create_light_theme() -> Theme {
        Theme {
            name: "Light".to_string(),
            description: "Clean light theme for MATRIX IDE".to_string(),
            author: "MATRIX IDE Team".to_string(),
            version: "1.0.0".to_string(),
            colors: ThemeColors {
                // === BACKGROUNDS - Light palette ===
                background: Color::rgb8(255, 255, 255),           // White - Background principale
                background_secondary: Color::rgb8(248, 250, 252), // Slate-50 - Pannelli secondari
                background_tertiary: Color::rgb8(241, 245, 249),  // Slate-100 - Elementi elevati

                // === TEXT - Dark text on light background ===
                text: Color::rgb8(15, 23, 42),                    // Slate-900 - Testo principale
                text_secondary: Color::rgb8(71, 85, 105),         // Slate-600 - Testo secondario
                text_tertiary: Color::rgb8(148, 163, 184),        // Slate-400 - Testo disabilitato

                // === ACCENTS - Matrix signature colors (adapted for light) ===
                accent: Color::rgb8(34, 197, 94),                 // Green-500 - Matrix green
                accent_secondary: Color::rgb8(22, 163, 74),       // Green-600 - Matrix green dark
                primary: Color::rgb8(59, 130, 246),               // Blue-500 - Primary actions

                // === INTERFACE ELEMENTS ===
                surface: Color::rgb8(248, 250, 252),              // Slate-50 - Cards, panels
                border: Color::rgb8(203, 213, 225),               // Slate-300 - Borders
                hover: Color::rgb8(241, 245, 249),                // Slate-100 - Hover state
                active: Color::rgb8(219, 234, 254),               // Blue-100 - Selection/Active state

                // === STATUS COLORS ===
                error: Color::rgb8(239, 68, 68),                  // Red-500 - Error
                warning: Color::rgb8(245, 158, 11),               // Amber-500 - Warning
                success: Color::rgb8(34, 197, 94),                // Green-500 - Success
                info: Color::rgb8(59, 130, 246),                  // Blue-500 - Info

                transparent: Color::TRANSPARENT,
                danger: Color::rgb8(239, 68, 68),                 // Red-500 - Danger
                danger_active: Color::rgb8(220, 38, 38),          // Red-600 - Danger active
            },
            spacing: ThemeSpacing::default(),
            font_sizes: ThemeFontSizes::default(),
            icons: ThemeIcons::default(),
            borders: ThemeBorders::default(),
            typography: ThemeTypography::default(),
            animations: ThemeAnimations::default(),
            syntax: SyntaxColors {
                keyword: Color::rgb8(147, 51, 234),               // Purple-600 - Keywords
                string: Color::rgb8(34, 197, 94),                 // Green-500 - Strings
                number: Color::rgb8(59, 130, 246),                // Blue-500 - Numbers
                comment: Color::rgb8(107, 114, 126),              // Gray-500 - Comments
                identifier: Color::rgb8(15, 23, 42),              // Slate-900 - Variables
                function: Color::rgb8(168, 85, 247),              // Purple-500 - Functions
                type_name: Color::rgb8(14, 165, 233),             // Sky-500 - Types
                operator: Color::rgb8(71, 85, 105),               // Slate-600 - Operators
                punctuation: Color::rgb8(71, 85, 105),            // Slate-600 - Punctuation
                preprocessor: Color::rgb8(107, 114, 126),         // Gray-500 - Preprocessor
                constant: Color::rgb8(59, 130, 246),              // Blue-500 - Constants
                builtin: Color::rgb8(147, 51, 234),               // Purple-600 - Built-ins
                error: Color::rgb8(239, 68, 68),                  // Red-500 - Errors
            },
            is_dark: false,
        }
    }

    /// Crea il tema high contrast
    fn create_high_contrast_theme() -> Theme {
        Theme {
            name: "High Contrast".to_string(),
            description: "High contrast theme for accessibility".to_string(),
            author: "MATRIX IDE Team".to_string(),
            version: "1.0.0".to_string(),
            colors: ThemeColors {
                // === BACKGROUNDS - High contrast ===
                background: Color::rgb8(0, 0, 0),                 // Pure black
                background_secondary: Color::rgb8(32, 32, 32),    // Dark gray
                background_tertiary: Color::rgb8(64, 64, 64),     // Medium gray

                // === TEXT - High contrast white ===
                text: Color::rgb8(255, 255, 255),                 // Pure white
                text_secondary: Color::rgb8(224, 224, 224),       // Light gray
                text_tertiary: Color::rgb8(192, 192, 192),        // Medium gray

                // === ACCENTS - Bright colors for visibility ===
                accent: Color::rgb8(0, 255, 0),                   // Bright green
                accent_secondary: Color::rgb8(0, 200, 0),         // Green
                primary: Color::rgb8(0, 150, 255),                // Bright blue

                // === INTERFACE ELEMENTS ===
                surface: Color::rgb8(32, 32, 32),                 // Dark gray
                border: Color::rgb8(128, 128, 128),               // Medium gray
                hover: Color::rgb8(64, 64, 64),                   // Hover state
                active: Color::rgb8(0, 100, 200),                 // Blue selection/Active state

                // === STATUS COLORS - High visibility ===
                error: Color::rgb8(255, 0, 0),                    // Bright red
                warning: Color::rgb8(255, 255, 0),                // Bright yellow
                success: Color::rgb8(0, 255, 0),                  // Bright green
                info: Color::rgb8(0, 255, 255),                   // Bright cyan

                transparent: Color::TRANSPARENT,
                danger: Color::rgb8(255, 0, 0),                   // Bright red
                danger_active: Color::rgb8(200, 0, 0),            // Red
            },
            spacing: ThemeSpacing::default(),
            font_sizes: ThemeFontSizes::default(),
            icons: ThemeIcons::default(),
            borders: ThemeBorders::default(),
            typography: ThemeTypography::default(),
            animations: ThemeAnimations::default(),
            syntax: SyntaxColors {
                keyword: Color::rgb8(255, 100, 255),              // Bright magenta
                string: Color::rgb8(100, 255, 100),               // Bright green
                number: Color::rgb8(100, 200, 255),               // Bright blue
                comment: Color::rgb8(128, 128, 128),              // Gray
                identifier: Color::rgb8(255, 255, 255),           // White
                function: Color::rgb8(255, 255, 100),             // Bright yellow
                type_name: Color::rgb8(100, 255, 255),            // Bright cyan
                operator: Color::rgb8(255, 255, 255),             // White
                punctuation: Color::rgb8(255, 255, 255),          // White
                preprocessor: Color::rgb8(255, 150, 100),         // Orange
                constant: Color::rgb8(150, 150, 255),             // Light blue
                builtin: Color::rgb8(255, 200, 100),              // Light orange
                error: Color::rgb8(255, 0, 0),                    // Bright red
            },
            is_dark: true,
        }
    }
}
