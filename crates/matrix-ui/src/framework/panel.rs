//! Panel System for MATRIX_IDE
//!
//! This module provides a professional panel system for organizing
//! UI components in sidebars, bottom panels, and floating windows.

use std::sync::Arc;
use floem::{View, reactive::{RwSignal, SignalUpdate, SignalWith}};
use crate::framework::component::{ProfessionalComponent, ComponentConfig, ComponentPosition};

/// Panel system for managing UI panels
pub struct PanelSystem {
    panels: RwSignal<std::collections::HashMap<String, Panel>>,
    panel_groups: RwSignal<std::collections::HashMap<String, PanelGroup>>,
}

impl PanelSystem {
    /// Creates a new panel system
    pub fn new() -> Self {
        Self {
            panels: RwSignal::new(std::collections::HashMap::new()),
            panel_groups: RwSignal::new(std::collections::HashMap::new()),
        }
    }

    /// Registers a panel
    pub fn register_panel(&self, panel: Panel) -> Result<(), PanelError> {
        self.panels.update(|panels| {
            panels.insert(panel.id.clone(), panel);
        });
        Ok(())
    }

    /// Gets a panel by ID
    pub fn get_panel(&self, panel_id: &str) -> Option<Panel> {
        self.panels.with(|panels| {
            panels.get(panel_id).cloned()
        })
    }

    /// Creates a panel group
    pub fn create_panel_group(&self, group: PanelGroup) -> Result<(), PanelError> {
        self.panel_groups.update(|groups| {
            groups.insert(group.id.clone(), group);
        });
        Ok(())
    }
}

/// Panel definition
#[derive(Clone)]
pub struct Panel {
    /// Panel identifier
    pub id: String,
    /// Panel title
    pub title: String,
    /// Panel icon
    pub icon: Option<String>,
    /// Panel position
    pub position: ComponentPosition,
    /// Panel component
    pub component: Arc<dyn ProfessionalComponent>,
    /// Panel configuration
    pub config: ComponentConfig,
    /// Panel state
    pub state: PanelState,
}

/// Panel state
#[derive(Debug, Clone)]
pub struct PanelState {
    /// Panel visibility
    pub visible: bool,
    /// Panel active state
    pub active: bool,
    /// Panel size
    pub size: (f64, f64),
    /// Panel position
    pub position: (f64, f64),
    /// Panel z-index
    pub z_index: i32,
}

impl Default for PanelState {
    fn default() -> Self {
        Self {
            visible: true,
            active: false,
            size: (300.0, 200.0),
            position: (0.0, 0.0),
            z_index: 0,
        }
    }
}

/// Panel group for organizing related panels
#[derive(Debug, Clone)]
pub struct PanelGroup {
    /// Group identifier
    pub id: String,
    /// Group title
    pub title: String,
    /// Group panels
    pub panels: Vec<String>,
    /// Group layout
    pub layout: PanelGroupLayout,
    /// Group state
    pub state: PanelGroupState,
}

/// Panel group layout
#[derive(Debug, Clone)]
pub enum PanelGroupLayout {
    Tabs,
    Stack,
    Grid { rows: usize, cols: usize },
    Custom(String),
}

/// Panel group state
#[derive(Debug, Clone)]
pub struct PanelGroupState {
    /// Active panel
    pub active_panel: Option<String>,
    /// Group visibility
    pub visible: bool,
    /// Group collapsed state
    pub collapsed: bool,
}

impl Default for PanelGroupState {
    fn default() -> Self {
        Self {
            active_panel: None,
            visible: true,
            collapsed: false,
        }
    }
}

/// Panel errors
#[derive(Debug, Clone)]
pub enum PanelError {
    PanelNotFound(String),
    InvalidConfiguration(String),
    LayoutError(String),
}

impl std::fmt::Display for PanelError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::PanelNotFound(id) => write!(f, "Panel not found: {}", id),
            Self::InvalidConfiguration(msg) => write!(f, "Invalid configuration: {}", msg),
            Self::LayoutError(msg) => write!(f, "Layout error: {}", msg),
        }
    }
}

impl std::error::Error for PanelError {}
