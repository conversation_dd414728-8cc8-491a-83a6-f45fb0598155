//! Tab System for MATRIX_IDE
//!
//! This module provides a professional tab system for organizing content.

use floem::{View, reactive::{RwSignal, SignalUpdate}};

/// Tab system for managing tabbed content
pub struct TabSystem {
    tabs: RwSignal<Vec<Tab>>,
    active_tab: RwSignal<Option<String>>,
}

impl TabSystem {
    /// Creates a new tab system
    pub fn new() -> Self {
        Self {
            tabs: RwSignal::new(Vec::new()),
            active_tab: RwSignal::new(None),
        }
    }

    /// Adds a tab
    pub fn add_tab(&self, tab: Tab) {
        self.tabs.update(|tabs| {
            tabs.push(tab);
        });
    }

    /// Removes a tab
    pub fn remove_tab(&self, tab_id: &str) {
        self.tabs.update(|tabs| {
            tabs.retain(|tab| tab.id != tab_id);
        });
    }

    /// Sets the active tab
    pub fn set_active_tab(&self, tab_id: Option<String>) {
        self.active_tab.set(tab_id);
    }
}

/// Tab definition
#[derive(Debug, Clone)]
pub struct Tab {
    /// Tab identifier
    pub id: String,
    /// Tab title
    pub title: String,
    /// Tab icon
    pub icon: Option<String>,
    /// Tab closable
    pub closable: bool,
    /// Tab content
    pub content: TabContent,
}

/// Tab content
#[derive(Debug, Clone)]
pub enum TabContent {
    Component(String), // Component ID
    Custom(String),    // Custom content
}

/// Tab state
#[derive(Debug, Clone)]
pub struct TabState {
    /// Tab active state
    pub active: bool,
    /// Tab visible state
    pub visible: bool,
    /// Tab loading state
    pub loading: bool,
    /// Tab modified state
    pub modified: bool,
}
