//! Professional Component System for MATRIX_IDE
//!
//! This module provides a comprehensive component system that enables
//! professional-grade UI components with consistent behavior and styling.

use std::sync::Arc;
use floem::{View, reactive::RwSignal, views::{container, v_stack, h_stack, label, Decorators}};
use serde::{Serialize, Deserialize};
use crate::{error::UiError, theme::{Theme, ThemeManager}};

/// Professional component trait for all UI components
pub trait ProfessionalComponent: Send + Sync {
    /// Component identifier
    fn id(&self) -> &str;
    
    /// Component display name
    fn name(&self) -> &str;
    
    /// Component category for organization
    fn category(&self) -> ComponentCategory;
    
    /// Creates the component view with professional styling
    fn create_view(&self, theme: &Theme) -> Result<Box<dyn View>, UiError>;
    
    /// Updates component state
    fn update(&mut self, message: ComponentMessage) -> Result<(), UiError>;
    
    /// Gets component configuration
    fn get_config(&self) -> ComponentConfig;
    
    /// Sets component configuration
    fn set_config(&mut self, config: ComponentConfig) -> Result<(), UiError>;
    
    /// Component lifecycle hooks
    fn on_mount(&mut self) -> Result<(), UiError> { Ok(()) }
    fn on_unmount(&mut self) -> Result<(), UiError> { Ok(()) }
    fn on_focus(&mut self) -> Result<(), UiError> { Ok(()) }
    fn on_blur(&mut self) -> Result<(), UiError> { Ok(()) }
    fn on_resize(&mut self, width: f64, height: f64) -> Result<(), UiError> { Ok(()) }
    
    /// Component validation
    fn validate(&self) -> Result<(), ComponentValidationError> { Ok(()) }
    
    /// Component accessibility information
    fn accessibility_info(&self) -> AccessibilityInfo {
        AccessibilityInfo::default()
    }
}

/// Component identifier
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ComponentId(pub u64);

/// Component categories for organization and management
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ComponentCategory {
    /// Core IDE components (editor, file explorer, etc.)
    Core,
    /// AI and intelligent features
    AI,
    /// Visualization and analysis
    Visualization,
    /// Development tools (terminal, debugger, etc.)
    Tools,
    /// Plugin-provided components
    Plugin,
    /// System and utility components
    System,
    /// Custom user components
    Custom,
}

/// Component configuration with professional standards
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentConfig {
    /// Component visibility
    pub visible: bool,
    
    /// Component position in the UI
    pub position: ComponentPosition,
    
    /// Component size specification
    pub size: ComponentSize,
    
    /// Whether the component can be resized
    pub resizable: bool,
    
    /// Whether the component can be closed
    pub closable: bool,
    
    /// Whether the component can be moved
    pub movable: bool,
    
    /// Component z-index for layering
    pub z_index: i32,
    
    /// Component opacity (0.0 to 1.0)
    pub opacity: f64,
    
    /// Component custom properties
    pub properties: std::collections::HashMap<String, ComponentProperty>,
    
    /// Component keyboard shortcuts
    pub shortcuts: Vec<KeyboardShortcut>,
    
    /// Component accessibility settings
    pub accessibility: AccessibilityConfig,
}

impl Default for ComponentConfig {
    fn default() -> Self {
        Self {
            visible: true,
            position: ComponentPosition::CenterArea,
            size: ComponentSize::default(),
            resizable: true,
            closable: true,
            movable: true,
            z_index: 0,
            opacity: 1.0,
            properties: std::collections::HashMap::new(),
            shortcuts: Vec::new(),
            accessibility: AccessibilityConfig::default(),
        }
    }
}

/// Component position in the UI layout
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComponentPosition {
    /// Left sidebar panel
    LeftSidebar,
    /// Right sidebar panel
    RightSidebar,
    /// Bottom panel
    BottomPanel,
    /// Center area (main content)
    CenterArea,
    /// Floating window with position
    Floating { x: f64, y: f64 },
    /// Modal dialog
    Modal,
    /// Custom position with constraints
    Custom { 
        container: String,
        constraints: PositionConstraints,
    },
}

/// Position constraints for custom positioning
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionConstraints {
    pub min_x: Option<f64>,
    pub max_x: Option<f64>,
    pub min_y: Option<f64>,
    pub max_y: Option<f64>,
    pub snap_to_grid: bool,
    pub grid_size: f64,
}

/// Component size specification with professional constraints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentSize {
    /// Width specification
    pub width: SizeSpec,
    /// Height specification
    pub height: SizeSpec,
    /// Minimum width constraint
    pub min_width: Option<f64>,
    /// Minimum height constraint
    pub min_height: Option<f64>,
    /// Maximum width constraint
    pub max_width: Option<f64>,
    /// Maximum height constraint
    pub max_height: Option<f64>,
    /// Aspect ratio constraint
    pub aspect_ratio: Option<f64>,
    /// Whether to maintain aspect ratio
    pub maintain_aspect_ratio: bool,
}

impl Default for ComponentSize {
    fn default() -> Self {
        Self {
            width: SizeSpec::Auto,
            height: SizeSpec::Auto,
            min_width: Some(100.0),
            min_height: Some(50.0),
            max_width: None,
            max_height: None,
            aspect_ratio: None,
            maintain_aspect_ratio: false,
        }
    }
}

/// Size specification options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SizeSpec {
    /// Fixed size in pixels
    Fixed(f64),
    /// Percentage of parent container
    Percentage(f64),
    /// Automatic sizing based on content
    Auto,
    /// Fill available space
    Fill,
    /// Minimum content size
    MinContent,
    /// Maximum content size
    MaxContent,
    /// Fit content with constraints
    FitContent { min: f64, max: f64 },
}

/// Component property value with type safety
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComponentProperty {
    String(String),
    Number(f64),
    Integer(i64),
    Boolean(bool),
    Color(String), // CSS color string
    Array(Vec<ComponentProperty>),
    Object(std::collections::HashMap<String, ComponentProperty>),
    Null,
}

/// Keyboard shortcut definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyboardShortcut {
    pub key: String,
    pub modifiers: Vec<KeyModifier>,
    pub action: String,
    pub description: String,
}

/// Keyboard modifiers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum KeyModifier {
    Ctrl,
    Alt,
    Shift,
    Meta, // Cmd on Mac, Windows key on Windows
}

/// Accessibility configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessibilityConfig {
    pub label: Option<String>,
    pub description: Option<String>,
    pub role: AccessibilityRole,
    pub focusable: bool,
    pub tab_index: Option<i32>,
    pub aria_attributes: std::collections::HashMap<String, String>,
}

impl Default for AccessibilityConfig {
    fn default() -> Self {
        Self {
            label: None,
            description: None,
            role: AccessibilityRole::Generic,
            focusable: true,
            tab_index: None,
            aria_attributes: std::collections::HashMap::new(),
        }
    }
}

/// Accessibility roles
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AccessibilityRole {
    Button,
    Link,
    TextBox,
    Label,
    Heading,
    List,
    ListItem,
    Tab,
    TabPanel,
    Dialog,
    Menu,
    MenuItem,
    Tree,
    TreeItem,
    Generic,
    Custom(String),
}

/// Accessibility information
#[derive(Debug, Clone)]
pub struct AccessibilityInfo {
    pub label: Option<String>,
    pub description: Option<String>,
    pub role: AccessibilityRole,
    pub state: AccessibilityState,
}

impl Default for AccessibilityInfo {
    fn default() -> Self {
        Self {
            label: None,
            description: None,
            role: AccessibilityRole::Generic,
            state: AccessibilityState::default(),
        }
    }
}

/// Accessibility state
#[derive(Debug, Clone, Default)]
pub struct AccessibilityState {
    pub focused: bool,
    pub selected: bool,
    pub expanded: bool,
    pub checked: Option<bool>,
    pub disabled: bool,
    pub readonly: bool,
}

/// Component messages for state updates
#[derive(Debug, Clone)]
pub enum ComponentMessage {
    /// Update component data
    UpdateData(serde_json::Value),
    /// Change component configuration
    Configure(ComponentConfig),
    /// Theme changed
    ThemeChanged(Theme),
    /// Focus/blur events
    Focus(bool),
    /// Resize event
    Resize { width: f64, height: f64 },
    /// Position change
    Move { x: f64, y: f64 },
    /// Visibility change
    SetVisible(bool),
    /// Custom component-specific message
    Custom(String, serde_json::Value),
    /// Keyboard event
    KeyboardEvent(KeyboardEvent),
    /// Mouse event
    MouseEvent(MouseEvent),
    /// Lifecycle event
    Lifecycle(LifecycleEvent),
}

/// Keyboard events
#[derive(Debug, Clone)]
pub struct KeyboardEvent {
    pub key: String,
    pub modifiers: Vec<KeyModifier>,
    pub event_type: KeyboardEventType,
}

/// Keyboard event types
#[derive(Debug, Clone)]
pub enum KeyboardEventType {
    KeyDown,
    KeyUp,
    KeyPress,
}

/// Mouse events
#[derive(Debug, Clone)]
pub struct MouseEvent {
    pub x: f64,
    pub y: f64,
    pub button: MouseButton,
    pub event_type: MouseEventType,
    pub modifiers: Vec<KeyModifier>,
}

/// Mouse buttons
#[derive(Debug, Clone)]
pub enum MouseButton {
    Left,
    Right,
    Middle,
    Other(u8),
}

/// Mouse event types
#[derive(Debug, Clone)]
pub enum MouseEventType {
    Click,
    DoubleClick,
    MouseDown,
    MouseUp,
    MouseMove,
    MouseEnter,
    MouseLeave,
    Wheel { delta_x: f64, delta_y: f64 },
}

/// Component lifecycle events
#[derive(Debug, Clone)]
pub enum LifecycleEvent {
    Mount,
    Unmount,
    Update,
    Render,
}

/// Component validation errors
#[derive(Debug, Clone)]
pub enum ComponentValidationError {
    InvalidConfiguration(String),
    MissingRequiredProperty(String),
    InvalidPropertyValue { property: String, value: String, expected: String },
    SizeConstraintViolation(String),
    PositionConstraintViolation(String),
    AccessibilityViolation(String),
}

impl std::fmt::Display for ComponentValidationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::InvalidConfiguration(msg) => write!(f, "Invalid configuration: {}", msg),
            Self::MissingRequiredProperty(prop) => write!(f, "Missing required property: {}", prop),
            Self::InvalidPropertyValue { property, value, expected } => {
                write!(f, "Invalid value '{}' for property '{}', expected: {}", value, property, expected)
            }
            Self::SizeConstraintViolation(msg) => write!(f, "Size constraint violation: {}", msg),
            Self::PositionConstraintViolation(msg) => write!(f, "Position constraint violation: {}", msg),
            Self::AccessibilityViolation(msg) => write!(f, "Accessibility violation: {}", msg),
        }
    }
}

impl std::error::Error for ComponentValidationError {}

/// Base component implementation for common functionality
pub struct BaseComponent {
    id: String,
    name: String,
    category: ComponentCategory,
    config: ComponentConfig,
    theme_manager: Arc<ThemeManager>,
    state: RwSignal<ComponentState>,
}

/// Component state
#[derive(Debug, Clone, Default)]
pub struct ComponentState {
    pub mounted: bool,
    pub focused: bool,
    pub visible: bool,
    pub size: (f64, f64),
    pub position: (f64, f64),
    pub data: serde_json::Value,
}

impl BaseComponent {
    pub fn new(
        id: String,
        name: String,
        category: ComponentCategory,
        theme_manager: Arc<ThemeManager>,
    ) -> Self {
        Self {
            id,
            name,
            category,
            config: ComponentConfig::default(),
            theme_manager,
            state: RwSignal::new(ComponentState::default()),
        }
    }

    /// Creates a professional container with theme styling
    pub fn create_professional_container(&self, content: impl View + 'static) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        
        container(content)
            .style(move |s| {
                s.background(theme.colors.background)
                    .border_radius(4.0)
                    .border(1.0)
                    .border_color(theme.colors.border)
                    .padding(8.0)
            })
    }

    /// Creates a professional header with title and controls
    pub fn create_professional_header(&self, title: &str) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let title = title.to_string();
        
        h_stack((
            label(move || title.clone())
                .style(move |s| {
                    s.font_bold()
                        .color(theme.colors.text)
                        .font_size(14.0)
                }),
            // TODO: Add header controls (close, minimize, etc.)
        ))
        .style(move |s| {
            s.padding(8.0)
                .background(theme.colors.background_secondary)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
                .justify_content(floem::style::JustifyContent::SpaceBetween)
        })
    }
}
