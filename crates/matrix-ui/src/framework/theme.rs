//! Theme System for MATRIX_IDE Framework
//!
//! This module provides theme integration for the professional UI framework.

use crate::theme::{Theme, ThemeManager};

/// Theme integration for the framework
pub struct FrameworkTheme {
    theme_manager: std::sync::Arc<ThemeManager>,
}

impl FrameworkTheme {
    /// Creates a new framework theme
    pub fn new(theme_manager: std::sync::Arc<ThemeManager>) -> Self {
        Self { theme_manager }
    }

    /// Gets the current theme
    pub fn get_theme(&self) -> Result<Theme, crate::error::UiError> {
        self.theme_manager.get_active_theme()
    }
}
