//! MATRIX_IDE Professional UI Framework
//!
//! This module provides a comprehensive, modular UI framework for MATRIX_IDE
//! that supports all components with professional standards and extensibility.

pub mod component;
pub mod layout;
pub mod panel;
pub mod state;
pub mod theme;
pub mod splitter;
pub mod tabs;
pub mod events;

use std::sync::Arc;
use floem::{View, reactive::{RwSignal, SignalGet, SignalUpdate, SignalWith}};
use crate::{error::UiError, theme::ThemeManager};

/// Professional UI Framework for MATRIX_IDE
pub struct MatrixUIFramework {
    /// Component registry for dynamic component management
    component_registry: Arc<ComponentRegistry>,
    
    /// Layout manager for professional panel management
    layout_manager: Arc<ProfessionalLayoutManager>,
    
    /// State manager for reactive state management
    state_manager: Arc<StateManager>,
    
    /// Theme manager for consistent styling
    theme_manager: Arc<ThemeManager>,
    
    /// Event dispatcher for command handling
    event_dispatcher: Arc<EventDispatcher>,
    
    /// Plugin integration system
    plugin_system: Arc<PluginUISystem>,
}

impl MatrixUIFramework {
    /// Creates a new professional UI framework instance
    pub fn new() -> Result<Self, UiError> {
        let theme_manager = Arc::new(ThemeManager::new()?);
        let state_manager = Arc::new(StateManager::new());
        let event_dispatcher = Arc::new(EventDispatcher::new());
        let component_registry = Arc::new(ComponentRegistry::new());
        let layout_manager = Arc::new(ProfessionalLayoutManager::new(
            theme_manager.clone(),
            state_manager.clone(),
        )?);
        let plugin_system = Arc::new(PluginUISystem::new(
            component_registry.clone(),
            event_dispatcher.clone(),
        )?);

        Ok(Self {
            component_registry,
            layout_manager,
            state_manager,
            theme_manager,
            event_dispatcher,
            plugin_system,
        })
    }

    /// Builds the complete professional UI
    pub fn build_ui(&self) -> Result<impl View, UiError> {
        self.layout_manager.build_professional_layout()
    }

    /// Registers a new component with the framework
    pub fn register_component<T: ProfessionalComponent + 'static>(
        &self,
        component: T,
    ) -> Result<ComponentId, UiError> {
        self.component_registry.register(Box::new(component))
    }

    /// Gets the current UI state
    pub fn get_state(&self) -> &StateManager {
        &self.state_manager
    }

    /// Gets the theme manager
    pub fn get_theme_manager(&self) -> &ThemeManager {
        &self.theme_manager
    }

    /// Dispatches an event through the system
    pub fn dispatch_event(&self, event: UIEvent) -> Result<(), UiError> {
        self.event_dispatcher.dispatch(event)
    }
}

/// Component registry for dynamic component management
pub struct ComponentRegistry {
    components: RwSignal<std::collections::HashMap<ComponentId, Box<dyn ProfessionalComponent>>>,
    next_id: RwSignal<u64>,
}

impl ComponentRegistry {
    pub fn new() -> Self {
        Self {
            components: RwSignal::new(std::collections::HashMap::new()),
            next_id: RwSignal::new(1),
        }
    }

    pub fn register(&self, component: Box<dyn ProfessionalComponent>) -> Result<ComponentId, UiError> {
        let id = ComponentId(self.next_id.get());
        self.next_id.update(|n| *n += 1);
        
        self.components.update(|components| {
            components.insert(id, component);
        });
        
        Ok(id)
    }

    pub fn get_component(&self, id: ComponentId) -> bool {
        self.components.with(|components| {
            components.contains_key(&id)
        })
    }
}

/// Professional component trait for all UI components
pub trait ProfessionalComponent: Send + Sync {
    /// Component identifier
    fn id(&self) -> &str;
    
    /// Component display name
    fn name(&self) -> &str;
    
    /// Component category for organization
    fn category(&self) -> ComponentCategory;
    
    /// Creates the component view
    fn create_view(&self) -> Result<Box<dyn View>, UiError>;
    
    /// Updates component state
    fn update(&mut self, message: ComponentMessage) -> Result<(), UiError>;
    
    /// Gets component configuration
    fn get_config(&self) -> ComponentConfig;
    
    /// Sets component configuration
    fn set_config(&mut self, config: ComponentConfig) -> Result<(), UiError>;
    
    /// Component lifecycle hooks
    fn on_mount(&mut self) -> Result<(), UiError> { Ok(()) }
    fn on_unmount(&mut self) -> Result<(), UiError> { Ok(()) }
    fn on_focus(&mut self) -> Result<(), UiError> { Ok(()) }
    fn on_blur(&mut self) -> Result<(), UiError> { Ok(()) }
}

/// Component categories for organization
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum ComponentCategory {
    /// Core IDE components (editor, file explorer, etc.)
    Core,
    /// AI and intelligent features
    AI,
    /// Visualization and analysis
    Visualization,
    /// Development tools (terminal, debugger, etc.)
    Tools,
    /// Plugin-provided components
    Plugin,
    /// System and utility components
    System,
}

/// Component identifier
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct ComponentId(u64);

/// Component configuration
#[derive(Debug, Clone)]
pub struct ComponentConfig {
    pub visible: bool,
    pub position: ComponentPosition,
    pub size: ComponentSize,
    pub resizable: bool,
    pub closable: bool,
    pub movable: bool,
    pub properties: std::collections::HashMap<String, ComponentProperty>,
}

/// Component position in the UI
#[derive(Debug, Clone)]
pub enum ComponentPosition {
    LeftSidebar,
    RightSidebar,
    BottomPanel,
    CenterArea,
    Floating { x: f64, y: f64 },
    Modal,
}

/// Component size specification
#[derive(Debug, Clone)]
pub struct ComponentSize {
    pub width: SizeSpec,
    pub height: SizeSpec,
    pub min_width: Option<f64>,
    pub min_height: Option<f64>,
    pub max_width: Option<f64>,
    pub max_height: Option<f64>,
}

/// Size specification
#[derive(Debug, Clone)]
pub enum SizeSpec {
    Fixed(f64),
    Percentage(f64),
    Auto,
    Fill,
}

/// Component property value
#[derive(Debug, Clone)]
pub enum ComponentProperty {
    String(String),
    Number(f64),
    Boolean(bool),
    Color(floem::peniko::Color),
    Array(Vec<ComponentProperty>),
    Object(std::collections::HashMap<String, ComponentProperty>),
}

/// Component messages for updates
#[derive(Debug, Clone)]
pub enum ComponentMessage {
    /// Update component data
    UpdateData(serde_json::Value),
    /// Change component configuration
    Configure(ComponentConfig),
    /// Theme changed
    ThemeChanged(crate::theme::Theme),
    /// Focus/blur events
    Focus(bool),
    /// Resize event
    Resize { width: f64, height: f64 },
    /// Custom component-specific message
    Custom(String, serde_json::Value),
}

/// UI events for the event system
#[derive(Debug, Clone)]
pub enum UIEvent {
    /// Command execution
    Command(String, serde_json::Value),
    /// Component interaction
    ComponentEvent(ComponentId, ComponentMessage),
    /// Layout change
    LayoutChanged(LayoutEvent),
    /// Theme change
    ThemeChanged(String),
    /// File operation
    FileOperation(FileEvent),
    /// Editor event
    EditorEvent(EditorEvent),
    /// AI interaction
    AIEvent(AIEvent),
}

/// Layout events
#[derive(Debug, Clone)]
pub enum LayoutEvent {
    PanelResized { panel: String, size: ComponentSize },
    PanelMoved { panel: String, position: ComponentPosition },
    PanelClosed { panel: String },
    PanelOpened { panel: String },
    SplitterMoved { splitter: String, position: f64 },
}

/// File events
#[derive(Debug, Clone)]
pub enum FileEvent {
    Opened { path: std::path::PathBuf },
    Closed { path: std::path::PathBuf },
    Modified { path: std::path::PathBuf },
    Saved { path: std::path::PathBuf },
    Created { path: std::path::PathBuf },
    Deleted { path: std::path::PathBuf },
}

/// Editor events
#[derive(Debug, Clone)]
pub enum EditorEvent {
    TextChanged { file: std::path::PathBuf, content: String },
    CursorMoved { file: std::path::PathBuf, line: usize, column: usize },
    SelectionChanged { file: std::path::PathBuf, selection: String },
    TabSwitched { from: Option<std::path::PathBuf>, to: std::path::PathBuf },
}

/// AI events
#[derive(Debug, Clone)]
pub enum AIEvent {
    MessageSent { message: String },
    ResponseReceived { response: String },
    AnalysisRequested { context: String },
    SuggestionGenerated { suggestion: String },
}

/// Event dispatcher for handling UI events
pub struct EventDispatcher {
    handlers: RwSignal<std::collections::HashMap<String, Box<dyn EventHandler>>>,
}

impl EventDispatcher {
    pub fn new() -> Self {
        Self {
            handlers: RwSignal::new(std::collections::HashMap::new()),
        }
    }

    pub fn register_handler(&self, event_type: String, handler: Box<dyn EventHandler>) {
        self.handlers.update(|handlers| {
            handlers.insert(event_type, handler);
        });
    }

    pub fn dispatch(&self, event: UIEvent) -> Result<(), UiError> {
        // Event dispatching logic
        Ok(())
    }
}

/// Event handler trait
pub trait EventHandler: Send + Sync {
    fn handle(&self, event: &UIEvent) -> Result<(), UiError>;
}

/// Plugin UI system for extensibility
pub struct PluginUISystem {
    component_registry: Arc<ComponentRegistry>,
    event_dispatcher: Arc<EventDispatcher>,
    plugin_components: RwSignal<std::collections::HashMap<String, Vec<ComponentId>>>,
}

impl PluginUISystem {
    pub fn new(
        component_registry: Arc<ComponentRegistry>,
        event_dispatcher: Arc<EventDispatcher>,
    ) -> Result<Self, UiError> {
        Ok(Self {
            component_registry,
            event_dispatcher,
            plugin_components: RwSignal::new(std::collections::HashMap::new()),
        })
    }

    pub fn register_plugin_component(
        &self,
        plugin_id: &str,
        component: Box<dyn ProfessionalComponent>,
    ) -> Result<ComponentId, UiError> {
        let component_id = self.component_registry.register(component)?;
        
        self.plugin_components.update(|components| {
            components.entry(plugin_id.to_string())
                .or_insert_with(Vec::new)
                .push(component_id);
        });
        
        Ok(component_id)
    }
}

/// State manager for reactive state management
pub struct StateManager {
    global_state: RwSignal<GlobalUIState>,
}

impl StateManager {
    pub fn new() -> Self {
        Self {
            global_state: RwSignal::new(GlobalUIState::default()),
        }
    }

    pub fn get_global_state(&self) -> &RwSignal<GlobalUIState> {
        &self.global_state
    }
}

/// Global UI state
#[derive(Debug, Clone, Default)]
pub struct GlobalUIState {
    pub panels: PanelStates,
    pub editor: EditorStates,
    pub layout: LayoutStates,
    pub theme: String,
}

/// Panel states
#[derive(Debug, Clone, Default)]
pub struct PanelStates {
    pub left_sidebar: SidebarState,
    pub right_sidebar: SidebarState,
    pub bottom_panel: PanelState,
    pub floating_panels: Vec<FloatingPanelState>,
}

/// Sidebar state
#[derive(Debug, Clone, Default)]
pub struct SidebarState {
    pub visible: bool,
    pub width: f64,
    pub active_tab: Option<String>,
    pub tabs: Vec<String>,
}

/// Panel state
#[derive(Debug, Clone, Default)]
pub struct PanelState {
    pub visible: bool,
    pub height: f64,
    pub active_tab: Option<String>,
    pub tabs: Vec<String>,
}

/// Floating panel state
#[derive(Debug, Clone)]
pub struct FloatingPanelState {
    pub id: String,
    pub position: (f64, f64),
    pub size: (f64, f64),
    pub visible: bool,
    pub z_index: i32,
}

/// Editor states
#[derive(Debug, Clone, Default)]
pub struct EditorStates {
    pub open_files: Vec<std::path::PathBuf>,
    pub active_file: Option<std::path::PathBuf>,
    pub split_layout: EditorSplitLayout,
}

/// Editor split layout
#[derive(Debug, Clone, Default)]
pub enum EditorSplitLayout {
    #[default]
    Single,
    Horizontal(Vec<std::path::PathBuf>),
    Vertical(Vec<std::path::PathBuf>),
    Grid { rows: usize, cols: usize, files: Vec<Option<std::path::PathBuf>> },
}

/// Layout states
#[derive(Debug, Clone, Default)]
pub struct LayoutStates {
    pub splitter_positions: std::collections::HashMap<String, f64>,
    pub panel_sizes: std::collections::HashMap<String, ComponentSize>,
    pub window_size: (f64, f64),
}

/// Professional layout manager
pub struct ProfessionalLayoutManager {
    theme_manager: Arc<ThemeManager>,
    state_manager: Arc<StateManager>,
}

impl ProfessionalLayoutManager {
    pub fn new(
        theme_manager: Arc<ThemeManager>,
        state_manager: Arc<StateManager>,
    ) -> Result<Self, UiError> {
        Ok(Self {
            theme_manager,
            state_manager,
        })
    }

    pub fn build_professional_layout(&self) -> Result<impl View, UiError> {
        // This will be implemented in the next phase
        use floem::views::{container, label};
        Ok(container(label(|| "Professional Layout - Coming Soon".to_string())))
    }
}
