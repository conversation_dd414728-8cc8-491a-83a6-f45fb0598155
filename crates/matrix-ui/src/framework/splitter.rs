//! Splitter System for MATRIX_IDE
//!
//! This module provides professional splitters for resizable panels.

use floem::{View, reactive::{RwSignal, SignalGet}, views::{container, empty, Decorators}, style::{Position, CursorStyle}};

/// Splitter direction
#[derive(Debug, Clone, PartialEq)]
pub enum SplitterDirection {
    Horizontal,
    Vertical,
}

/// Splitter configuration
#[derive(Debug, Clone)]
pub struct SplitterConfig {
    pub direction: SplitterDirection,
    pub size: f64,
    pub min_size: f64,
    pub max_size: f64,
    pub snap_threshold: f64,
}

impl Default for SplitterConfig {
    fn default() -> Self {
        Self {
            direction: SplitterDirection::Vertical,
            size: 4.0,
            min_size: 100.0,
            max_size: 800.0,
            snap_threshold: 20.0,
        }
    }
}

/// Creates a professional splitter
pub fn create_splitter(
    config: SplitterConfig,
    position: RwSignal<f64>,
    is_dragging: RwSignal<bool>,
) -> impl View {
    let cursor = match config.direction {
        SplitterDirection::Horizontal => CursorStyle::RowResize,
        SplitterDirection::Vertical => CursorStyle::ColResize,
    };

    container(empty())
        .style(move |s| {
            s.position(Position::Absolute)
                .z_index(10)
                .cursor(cursor)
                .apply_if(config.direction == SplitterDirection::Vertical, |s| {
                    s.width(config.size).height_full()
                })
                .apply_if(config.direction == SplitterDirection::Horizontal, |s| {
                    s.height(config.size).width_full()
                })
                .hover(|s| s.background(floem::peniko::Color::rgb8(0, 122, 255)))
                .apply_if(is_dragging.get(), |s| s.background(floem::peniko::Color::rgb8(0, 122, 255)))
        })
}
