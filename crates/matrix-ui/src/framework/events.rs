//! Event System for MATRIX_IDE
//!
//! This module provides a comprehensive event system for handling
//! UI events, commands, and inter-component communication.

use std::sync::Arc;
use floem::reactive::{RwSignal, SignalGet, SignalUpdate, SignalWith};
use serde::{Serialize, Deserialize};

/// Event dispatcher for handling UI events
pub struct EventDispatcher {
    handlers: RwSignal<std::collections::HashMap<String, Vec<Box<dyn EventHandler>>>>,
    command_registry: Arc<CommandRegistry>,
    event_history: RwSignal<Vec<UIEvent>>,
    max_history_size: usize,
}

impl EventDispatcher {
    /// Creates a new event dispatcher
    pub fn new() -> Self {
        Self {
            handlers: RwSignal::new(std::collections::HashMap::new()),
            command_registry: Arc::new(CommandRegistry::new()),
            event_history: RwSignal::new(Vec::new()),
            max_history_size: 1000,
        }
    }

    /// Registers an event handler
    pub fn register_handler(&self, event_type: String, handler: Box<dyn EventHandler>) {
        self.handlers.update(|handlers| {
            handlers.entry(event_type).or_insert_with(Vec::new).push(handler);
        });
    }

    /// Dispatches an event to all registered handlers
    pub fn dispatch(&self, event: UIEvent) -> Result<(), EventError> {
        // Add to history
        self.event_history.update(|history| {
            history.push(event.clone());
            if history.len() > self.max_history_size {
                history.remove(0);
            }
        });

        // Get event type
        let event_type = event.event_type();

        // Dispatch to handlers
        self.handlers.with(|handlers| {
            if let Some(event_handlers) = handlers.get(&event_type) {
                for handler in event_handlers {
                    if let Err(e) = handler.handle(&event) {
                        eprintln!("Event handler error: {}", e);
                    }
                }
            }
        });

        Ok(())
    }

    /// Dispatches a command
    pub fn dispatch_command(&self, command: Command) -> Result<(), EventError> {
        self.command_registry.execute(command)
    }

    /// Gets the command registry
    pub fn get_command_registry(&self) -> &CommandRegistry {
        &self.command_registry
    }

    /// Gets event history
    pub fn get_event_history(&self) -> Vec<UIEvent> {
        self.event_history.get()
    }

    /// Clears event history
    pub fn clear_event_history(&self) {
        self.event_history.update(|history| history.clear());
    }
}

/// Event handler trait
pub trait EventHandler: Send + Sync {
    /// Handles an event
    fn handle(&self, event: &UIEvent) -> Result<(), EventError>;
    
    /// Gets the handler name
    fn name(&self) -> &str;
    
    /// Gets the handler priority (higher = executed first)
    fn priority(&self) -> i32 { 0 }
}

/// UI events for the event system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UIEvent {
    /// Command execution
    Command(Command),
    /// Component interaction
    ComponentEvent { 
        component_id: String,
        event_type: String,
        data: serde_json::Value,
    },
    /// Layout change
    LayoutChanged(LayoutEvent),
    /// Theme change
    ThemeChanged { 
        old_theme: String,
        new_theme: String,
    },
    /// File operation
    FileOperation(FileEvent),
    /// Editor event
    EditorEvent(EditorEvent),
    /// AI interaction
    AIEvent(AIEvent),
    /// Window event
    WindowEvent(WindowEvent),
    /// Keyboard event
    KeyboardEvent(KeyboardEvent),
    /// Mouse event
    MouseEvent(MouseEvent),
    /// Plugin event
    PluginEvent(PluginEvent),
    /// System event
    SystemEvent(SystemEvent),
}

impl UIEvent {
    /// Gets the event type as a string
    pub fn event_type(&self) -> String {
        match self {
            Self::Command(_) => "command".to_string(),
            Self::ComponentEvent { .. } => "component".to_string(),
            Self::LayoutChanged(_) => "layout".to_string(),
            Self::ThemeChanged { .. } => "theme".to_string(),
            Self::FileOperation(_) => "file".to_string(),
            Self::EditorEvent(_) => "editor".to_string(),
            Self::AIEvent(_) => "ai".to_string(),
            Self::WindowEvent(_) => "window".to_string(),
            Self::KeyboardEvent(_) => "keyboard".to_string(),
            Self::MouseEvent(_) => "mouse".to_string(),
            Self::PluginEvent(_) => "plugin".to_string(),
            Self::SystemEvent(_) => "system".to_string(),
        }
    }
}

/// Command for executing actions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Command {
    /// Command identifier
    pub id: String,
    /// Command name
    pub name: String,
    /// Command category
    pub category: String,
    /// Command parameters
    pub params: serde_json::Value,
    /// Command source
    pub source: CommandSource,
    /// Command timestamp
    pub timestamp: std::time::SystemTime,
}

/// Command source
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommandSource {
    Keyboard,
    Menu,
    Button,
    API,
    Plugin,
    System,
}

/// Layout events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LayoutEvent {
    PanelResized { 
        panel: String, 
        old_size: (f64, f64),
        new_size: (f64, f64),
    },
    PanelMoved { 
        panel: String, 
        old_position: (f64, f64),
        new_position: (f64, f64),
    },
    PanelClosed { panel: String },
    PanelOpened { panel: String },
    SplitterMoved { 
        splitter: String, 
        old_position: f64,
        new_position: f64,
    },
    LayoutModeChanged {
        old_mode: String,
        new_mode: String,
    },
}

/// File events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileEvent {
    Opened { path: std::path::PathBuf },
    Closed { path: std::path::PathBuf },
    Modified { 
        path: std::path::PathBuf,
        changes: Vec<FileChange>,
    },
    Saved { path: std::path::PathBuf },
    Created { path: std::path::PathBuf },
    Deleted { path: std::path::PathBuf },
    Renamed { 
        old_path: std::path::PathBuf,
        new_path: std::path::PathBuf,
    },
}

/// File change
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileChange {
    pub line: usize,
    pub column: usize,
    pub old_text: String,
    pub new_text: String,
}

/// Editor events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EditorEvent {
    TextChanged { 
        file: std::path::PathBuf, 
        changes: Vec<TextChange>,
    },
    CursorMoved { 
        file: std::path::PathBuf, 
        old_position: (usize, usize),
        new_position: (usize, usize),
    },
    SelectionChanged { 
        file: std::path::PathBuf, 
        selection: TextSelection,
    },
    TabSwitched { 
        from: Option<std::path::PathBuf>, 
        to: std::path::PathBuf,
    },
    SplitChanged {
        layout: String,
    },
    ScrollChanged {
        file: std::path::PathBuf,
        position: (f64, f64),
    },
}

/// Text change
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextChange {
    pub start_line: usize,
    pub start_column: usize,
    pub end_line: usize,
    pub end_column: usize,
    pub old_text: String,
    pub new_text: String,
}

/// Text selection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextSelection {
    pub start_line: usize,
    pub start_column: usize,
    pub end_line: usize,
    pub end_column: usize,
}

/// AI events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AIEvent {
    MessageSent { 
        message: String,
        context: serde_json::Value,
    },
    ResponseReceived { 
        response: String,
        metadata: serde_json::Value,
    },
    AnalysisRequested { 
        context: String,
        analysis_type: String,
    },
    SuggestionGenerated { 
        suggestion: String,
        confidence: f64,
    },
    ModelChanged {
        old_model: String,
        new_model: String,
    },
}

/// Window events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WindowEvent {
    Resized { 
        old_size: (f64, f64),
        new_size: (f64, f64),
    },
    Moved {
        old_position: (f64, f64),
        new_position: (f64, f64),
    },
    Focused,
    Unfocused,
    Minimized,
    Maximized,
    Restored,
    CloseRequested,
}

/// Keyboard events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyboardEvent {
    pub key: String,
    pub modifiers: Vec<String>,
    pub event_type: String, // "keydown", "keyup", "keypress"
    pub repeat: bool,
}

/// Mouse events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MouseEvent {
    pub x: f64,
    pub y: f64,
    pub button: String, // "left", "right", "middle"
    pub event_type: String, // "click", "mousedown", "mouseup", "mousemove"
    pub modifiers: Vec<String>,
    pub click_count: u32,
}

/// Plugin events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PluginEvent {
    Loaded { plugin_id: String },
    Unloaded { plugin_id: String },
    Error { 
        plugin_id: String,
        error: String,
    },
    Message {
        plugin_id: String,
        message: serde_json::Value,
    },
}

/// System events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SystemEvent {
    LowMemory,
    HighCPU,
    DiskSpaceLow,
    NetworkChanged,
    PowerStateChanged,
    ThemeChanged,
}

/// Command registry for managing commands
pub struct CommandRegistry {
    commands: RwSignal<std::collections::HashMap<String, Box<dyn CommandHandler>>>,
    shortcuts: RwSignal<std::collections::HashMap<String, String>>, // shortcut -> command_id
}

impl CommandRegistry {
    /// Creates a new command registry
    pub fn new() -> Self {
        Self {
            commands: RwSignal::new(std::collections::HashMap::new()),
            shortcuts: RwSignal::new(std::collections::HashMap::new()),
        }
    }

    /// Registers a command handler
    pub fn register_command(&self, command_id: String, handler: Box<dyn CommandHandler>) {
        self.commands.update(|commands| {
            commands.insert(command_id, handler);
        });
    }

    /// Registers a keyboard shortcut
    pub fn register_shortcut(&self, shortcut: String, command_id: String) {
        self.shortcuts.update(|shortcuts| {
            shortcuts.insert(shortcut, command_id);
        });
    }

    /// Executes a command
    pub fn execute(&self, command: Command) -> Result<(), EventError> {
        self.commands.with(|commands| {
            if let Some(handler) = commands.get(&command.id) {
                handler.execute(&command)
            } else {
                Err(EventError::CommandNotFound(command.id))
            }
        })
    }

    /// Gets command by shortcut
    pub fn get_command_by_shortcut(&self, shortcut: &str) -> Option<String> {
        self.shortcuts.with(|shortcuts| {
            shortcuts.get(shortcut).cloned()
        })
    }

    /// Gets all registered commands
    pub fn get_commands(&self) -> Vec<String> {
        self.commands.with(|commands| {
            commands.keys().cloned().collect()
        })
    }
}

/// Command handler trait
pub trait CommandHandler: Send + Sync {
    /// Executes the command
    fn execute(&self, command: &Command) -> Result<(), EventError>;
    
    /// Gets the command name
    fn name(&self) -> &str;
    
    /// Gets the command description
    fn description(&self) -> &str;
    
    /// Gets the command category
    fn category(&self) -> &str;
}

/// Event system errors
#[derive(Debug, Clone)]
pub enum EventError {
    HandlerError(String),
    CommandNotFound(String),
    InvalidEventData(String),
    SerializationError(String),
    DispatchError(String),
}

impl std::fmt::Display for EventError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::HandlerError(msg) => write!(f, "Handler error: {}", msg),
            Self::CommandNotFound(cmd) => write!(f, "Command not found: {}", cmd),
            Self::InvalidEventData(msg) => write!(f, "Invalid event data: {}", msg),
            Self::SerializationError(msg) => write!(f, "Serialization error: {}", msg),
            Self::DispatchError(msg) => write!(f, "Dispatch error: {}", msg),
        }
    }
}

impl std::error::Error for EventError {}

/// Event filter for selective event handling
pub struct EventFilter {
    event_types: Vec<String>,
    component_ids: Vec<String>,
    exclude_types: Vec<String>,
}

impl EventFilter {
    /// Creates a new event filter
    pub fn new() -> Self {
        Self {
            event_types: Vec::new(),
            component_ids: Vec::new(),
            exclude_types: Vec::new(),
        }
    }

    /// Adds an event type to filter
    pub fn add_event_type(mut self, event_type: String) -> Self {
        self.event_types.push(event_type);
        self
    }

    /// Adds a component ID to filter
    pub fn add_component_id(mut self, component_id: String) -> Self {
        self.component_ids.push(component_id);
        self
    }

    /// Excludes an event type
    pub fn exclude_event_type(mut self, event_type: String) -> Self {
        self.exclude_types.push(event_type);
        self
    }

    /// Checks if an event passes the filter
    pub fn matches(&self, event: &UIEvent) -> bool {
        let event_type = event.event_type();
        
        // Check exclusions first
        if self.exclude_types.contains(&event_type) {
            return false;
        }
        
        // If no specific filters, allow all (except excluded)
        if self.event_types.is_empty() && self.component_ids.is_empty() {
            return true;
        }
        
        // Check event type filter
        if !self.event_types.is_empty() && !self.event_types.contains(&event_type) {
            return false;
        }
        
        // Check component ID filter
        if !self.component_ids.is_empty() {
            if let UIEvent::ComponentEvent { component_id, .. } = event {
                if !self.component_ids.contains(component_id) {
                    return false;
                }
            } else {
                return false;
            }
        }
        
        true
    }
}
