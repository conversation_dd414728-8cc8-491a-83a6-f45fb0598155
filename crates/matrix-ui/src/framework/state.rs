//! State Management System for MATRIX_IDE
//!
//! This module provides a comprehensive state management system
//! for maintaining UI state across the application.

use floem::reactive::{RwSignal, SignalGet, SignalUpdate, SignalWith};
use serde::{Serialize, Deserialize};
use crate::framework::component::ComponentSize;

/// State manager for reactive state management
pub struct StateManager {
    global_state: RwSignal<GlobalUIState>,
    component_states: RwSignal<std::collections::HashMap<String, ComponentState>>,
    layout_states: RwSignal<LayoutStates>,
    editor_states: RwSignal<EditorStates>,
    panel_states: RwSignal<PanelStates>,
}

impl StateManager {
    /// Creates a new state manager
    pub fn new() -> Self {
        Self {
            global_state: RwSignal::new(GlobalUIState::default()),
            component_states: RwSignal::new(std::collections::HashMap::new()),
            layout_states: RwSignal::new(LayoutStates::default()),
            editor_states: RwSignal::new(EditorStates::default()),
            panel_states: RwSignal::new(PanelStates::default()),
        }
    }

    /// Gets the global UI state signal
    pub fn get_global_state(&self) -> &RwSignal<GlobalUIState> {
        &self.global_state
    }

    /// Gets the layout states signal
    pub fn get_layout_states(&self) -> &RwSignal<LayoutStates> {
        &self.layout_states
    }

    /// Gets the editor states signal
    pub fn get_editor_states(&self) -> &RwSignal<EditorStates> {
        &self.editor_states
    }

    /// Gets the panel states signal
    pub fn get_panel_states(&self) -> &RwSignal<PanelStates> {
        &self.panel_states
    }

    /// Updates component state
    pub fn update_component_state(&self, component_id: String, state: ComponentState) {
        self.component_states.update(|states| {
            states.insert(component_id, state);
        });
    }

    /// Gets component state
    pub fn get_component_state(&self, component_id: &str) -> Option<ComponentState> {
        self.component_states.with(|states| {
            states.get(component_id).cloned()
        })
    }

    /// Updates panel visibility
    pub fn set_panel_visible(&self, panel_id: &str, visible: bool) {
        self.panel_states.update(|states| {
            match panel_id {
                "left_sidebar" => states.left_sidebar.visible = visible,
                "right_sidebar" => states.right_sidebar.visible = visible,
                "bottom_panel" => states.bottom_panel.visible = visible,
                _ => {
                    // Handle floating panels
                    if let Some(panel) = states.floating_panels.iter_mut()
                        .find(|p| p.id == panel_id) {
                        panel.visible = visible;
                    }
                }
            }
        });
    }

    /// Updates panel size
    pub fn set_panel_size(&self, panel_id: &str, size: ComponentSize) {
        self.layout_states.update(|states| {
            states.panel_sizes.insert(panel_id.to_string(), size);
        });
    }

    /// Updates splitter position
    pub fn set_splitter_position(&self, splitter_id: &str, position: f64) {
        self.layout_states.update(|states| {
            states.splitter_positions.insert(splitter_id.to_string(), position);
        });
    }

    /// Opens a file in the editor
    pub fn open_file(&self, file_path: std::path::PathBuf) {
        self.editor_states.update(|states| {
            if !states.open_files.contains(&file_path) {
                states.open_files.push(file_path.clone());
            }
            states.active_file = Some(file_path);
        });
    }

    /// Closes a file in the editor
    pub fn close_file(&self, file_path: &std::path::Path) {
        self.editor_states.update(|states| {
            states.open_files.retain(|f| f != file_path);
            if states.active_file.as_ref().map(|p| p.as_path()) == Some(file_path) {
                states.active_file = states.open_files.first().cloned();
            }
        });
    }

    /// Sets the active file
    pub fn set_active_file(&self, file_path: Option<std::path::PathBuf>) {
        self.editor_states.update(|states| {
            states.active_file = file_path;
        });
    }

    /// Persists state to storage
    pub fn save_state(&self) -> Result<(), StateError> {
        // TODO: Implement state persistence
        Ok(())
    }

    /// Loads state from storage
    pub fn load_state(&self) -> Result<(), StateError> {
        // TODO: Implement state loading
        Ok(())
    }
}

/// Global UI state
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct GlobalUIState {
    /// Current theme name
    pub theme: String,
    /// Window size
    pub window_size: (f64, f64),
    /// Window position
    pub window_position: (f64, f64),
    /// Window maximized state
    pub window_maximized: bool,
    /// Application focus state
    pub focused: bool,
    /// Last active timestamp
    pub last_active: Option<std::time::SystemTime>,
}

/// Component state for individual components
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentState {
    /// Component mounted state
    pub mounted: bool,
    /// Component focused state
    pub focused: bool,
    /// Component visible state
    pub visible: bool,
    /// Component size
    pub size: (f64, f64),
    /// Component position
    pub position: (f64, f64),
    /// Component data
    pub data: serde_json::Value,
    /// Component configuration
    pub config: serde_json::Value,
    /// Last update timestamp
    pub last_updated: std::time::SystemTime,
}

impl Default for ComponentState {
    fn default() -> Self {
        Self {
            mounted: false,
            focused: false,
            visible: true,
            size: (0.0, 0.0),
            position: (0.0, 0.0),
            data: serde_json::Value::Null,
            config: serde_json::Value::Null,
            last_updated: std::time::SystemTime::now(),
        }
    }
}

/// Layout states for panel management
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct LayoutStates {
    /// Splitter positions
    pub splitter_positions: std::collections::HashMap<String, f64>,
    /// Panel sizes
    pub panel_sizes: std::collections::HashMap<String, ComponentSize>,
    /// Window size
    pub window_size: (f64, f64),
    /// Layout mode (normal, fullscreen, etc.)
    pub layout_mode: LayoutMode,
}

/// Layout modes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LayoutMode {
    Normal,
    Fullscreen,
    ZenMode,
    Presentation,
}

impl Default for LayoutMode {
    fn default() -> Self {
        Self::Normal
    }
}

/// Editor states
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct EditorStates {
    /// Open files
    pub open_files: Vec<std::path::PathBuf>,
    /// Active file
    pub active_file: Option<std::path::PathBuf>,
    /// Editor split layout
    pub split_layout: EditorSplitLayout,
    /// Editor settings
    pub settings: EditorSettings,
    /// Cursor positions for each file
    pub cursor_positions: std::collections::HashMap<std::path::PathBuf, CursorPosition>,
    /// Scroll positions for each file
    pub scroll_positions: std::collections::HashMap<std::path::PathBuf, ScrollPosition>,
}

/// Editor split layout
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EditorSplitLayout {
    Single,
    Horizontal(Vec<std::path::PathBuf>),
    Vertical(Vec<std::path::PathBuf>),
    Grid { 
        rows: usize, 
        cols: usize, 
        files: Vec<Option<std::path::PathBuf>>,
    },
}

impl Default for EditorSplitLayout {
    fn default() -> Self {
        Self::Single
    }
}

/// Editor settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditorSettings {
    /// Font family
    pub font_family: String,
    /// Font size
    pub font_size: f64,
    /// Line height
    pub line_height: f64,
    /// Tab size
    pub tab_size: usize,
    /// Use spaces for tabs
    pub use_spaces: bool,
    /// Word wrap
    pub word_wrap: bool,
    /// Show line numbers
    pub show_line_numbers: bool,
    /// Show minimap
    pub show_minimap: bool,
    /// Auto save
    pub auto_save: bool,
    /// Auto save delay (milliseconds)
    pub auto_save_delay: u64,
}

impl Default for EditorSettings {
    fn default() -> Self {
        Self {
            font_family: "JetBrains Mono, Consolas, monospace".to_string(),
            font_size: 14.0,
            line_height: 1.5,
            tab_size: 4,
            use_spaces: true,
            word_wrap: false,
            show_line_numbers: true,
            show_minimap: true,
            auto_save: true,
            auto_save_delay: 1000,
        }
    }
}

/// Cursor position in a file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CursorPosition {
    pub line: usize,
    pub column: usize,
    pub selection_start: Option<(usize, usize)>,
    pub selection_end: Option<(usize, usize)>,
}

impl Default for CursorPosition {
    fn default() -> Self {
        Self {
            line: 0,
            column: 0,
            selection_start: None,
            selection_end: None,
        }
    }
}

/// Scroll position in a file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScrollPosition {
    pub x: f64,
    pub y: f64,
}

impl Default for ScrollPosition {
    fn default() -> Self {
        Self { x: 0.0, y: 0.0 }
    }
}

/// Panel states
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PanelStates {
    /// Left sidebar state
    pub left_sidebar: SidebarState,
    /// Right sidebar state
    pub right_sidebar: SidebarState,
    /// Bottom panel state
    pub bottom_panel: PanelState,
    /// Floating panels
    pub floating_panels: Vec<FloatingPanelState>,
}

/// Sidebar state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SidebarState {
    /// Sidebar visibility
    pub visible: bool,
    /// Sidebar width
    pub width: f64,
    /// Active tab
    pub active_tab: Option<String>,
    /// Available tabs
    pub tabs: Vec<TabState>,
    /// Collapsed state
    pub collapsed: bool,
}

impl Default for SidebarState {
    fn default() -> Self {
        Self {
            visible: true,
            width: 280.0,
            active_tab: None,
            tabs: Vec::new(),
            collapsed: false,
        }
    }
}

/// Panel state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PanelState {
    /// Panel visibility
    pub visible: bool,
    /// Panel height
    pub height: f64,
    /// Active tab
    pub active_tab: Option<String>,
    /// Available tabs
    pub tabs: Vec<TabState>,
    /// Collapsed state
    pub collapsed: bool,
}

impl Default for PanelState {
    fn default() -> Self {
        Self {
            visible: true,
            height: 240.0,
            active_tab: None,
            tabs: Vec::new(),
            collapsed: false,
        }
    }
}

/// Tab state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TabState {
    /// Tab identifier
    pub id: String,
    /// Tab title
    pub title: String,
    /// Tab icon
    pub icon: Option<String>,
    /// Tab closable
    pub closable: bool,
    /// Tab active state
    pub active: bool,
    /// Tab data
    pub data: serde_json::Value,
}

/// Floating panel state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FloatingPanelState {
    /// Panel identifier
    pub id: String,
    /// Panel title
    pub title: String,
    /// Panel position
    pub position: (f64, f64),
    /// Panel size
    pub size: (f64, f64),
    /// Panel visibility
    pub visible: bool,
    /// Panel z-index
    pub z_index: i32,
    /// Panel resizable
    pub resizable: bool,
    /// Panel movable
    pub movable: bool,
    /// Panel minimized
    pub minimized: bool,
    /// Panel maximized
    pub maximized: bool,
}

/// State management errors
#[derive(Debug, Clone)]
pub enum StateError {
    SerializationError(String),
    DeserializationError(String),
    FileSystemError(String),
    ValidationError(String),
    LockError(String),
}

impl std::fmt::Display for StateError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::SerializationError(msg) => write!(f, "Serialization error: {}", msg),
            Self::DeserializationError(msg) => write!(f, "Deserialization error: {}", msg),
            Self::FileSystemError(msg) => write!(f, "File system error: {}", msg),
            Self::ValidationError(msg) => write!(f, "Validation error: {}", msg),
            Self::LockError(msg) => write!(f, "Lock error: {}", msg),
        }
    }
}

impl std::error::Error for StateError {}

/// State persistence manager
pub struct StatePersistence {
    config_dir: std::path::PathBuf,
}

impl StatePersistence {
    /// Creates a new state persistence manager
    pub fn new(config_dir: std::path::PathBuf) -> Self {
        Self { config_dir }
    }

    /// Saves state to file
    pub fn save_state(&self, state: &GlobalUIState) -> Result<(), StateError> {
        let state_file = self.config_dir.join("ui_state.json");
        let json = serde_json::to_string_pretty(state)
            .map_err(|e| StateError::SerializationError(e.to_string()))?;
        
        std::fs::write(&state_file, json)
            .map_err(|e| StateError::FileSystemError(e.to_string()))?;
        
        Ok(())
    }

    /// Loads state from file
    pub fn load_state(&self) -> Result<GlobalUIState, StateError> {
        let state_file = self.config_dir.join("ui_state.json");
        
        if !state_file.exists() {
            return Ok(GlobalUIState::default());
        }
        
        let json = std::fs::read_to_string(&state_file)
            .map_err(|e| StateError::FileSystemError(e.to_string()))?;
        
        let state = serde_json::from_str(&json)
            .map_err(|e| StateError::DeserializationError(e.to_string()))?;
        
        Ok(state)
    }
}
