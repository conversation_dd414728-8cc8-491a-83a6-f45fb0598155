//! Componente barra di stato
//!
//! Questo modulo fornisce una barra di stato personalizzata per l'IDE MATRIX.

use std::sync::Arc;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use floem::reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal};
use floem::{View, views::{h_stack, h_stack_from_iter, label, empty, container, button, dyn_stack, v_stack, Decorators}};
use floem::peniko::Color;
use uuid::Uuid;

use crate::theme::ThemeManager;
use crate::file_operations::{FileOperationProgress, FileOperationsEngine};
use matrix_core::Engine as CoreEngine;

/// Tipo di indicatore di stato
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[allow(dead_code)]
pub enum StatusType {
    /// Stato normale
    Normal,

    /// Stato di successo
    Success,

    /// Stato di errore
    Error,

    /// Stato di avviso
    Warning,

    /// Stato informativo
    Info,

    /// Stato di caricamento
    Loading,

    /// Stato di progresso
    Progress,
}

/// Notification types for the status bar
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum NotificationType {
    Info,
    Warning,
    Error,
    Success,
}

/// Status bar notification
#[derive(Debug, Clone)]
pub struct StatusNotification {
    pub id: Uuid,
    pub message: String,
    pub notification_type: NotificationType,
    pub timestamp: Instant,
    pub duration: Option<Duration>,
    pub dismissible: bool,
    pub action: Option<StatusAction>,
}

/// Action that can be triggered from a notification
#[derive(Debug, Clone)]
pub struct StatusAction {
    pub label: String,
    pub callback: Arc<dyn Fn() + Send + Sync>,
}

/// Progress indicator for the status bar
#[derive(Debug, Clone)]
pub struct ProgressIndicator {
    pub id: Uuid,
    pub label: String,
    pub progress: f32, // 0.0 to 1.0
    pub indeterminate: bool,
    pub cancellable: bool,
    pub cancel_callback: Option<Arc<dyn Fn() + Send + Sync>>,
}

/// System status information
#[derive(Debug, Clone)]
pub struct SystemStatus {
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub disk_usage: f32,
    pub network_activity: bool,
    pub git_status: Option<GitStatus>,
    pub lsp_status: HashMap<String, LspServerStatus>,
    pub build_status: Option<BuildStatus>,
}

/// Git repository status
#[derive(Debug, Clone)]
pub struct GitStatus {
    pub branch: String,
    pub ahead: usize,
    pub behind: usize,
    pub modified: usize,
    pub staged: usize,
    pub untracked: usize,
    pub conflicts: usize,
}

/// LSP server status
#[derive(Debug, Clone)]
pub enum LspServerStatus {
    Starting,
    Running,
    Error(String),
    Stopped,
}

/// Build status
#[derive(Debug, Clone)]
pub enum BuildStatus {
    Idle,
    Building,
    Success,
    Failed(String),
    Warning(Vec<String>),
}

/// Enhanced Status Bar Manager
#[derive(Clone)]
pub struct StatusBarManager {
    /// Core engine reference
    core: Arc<CoreEngine>,

    /// Theme manager
    theme_manager: Arc<ThemeManager>,

    /// File operations engine
    file_operations: Arc<FileOperationsEngine>,

    /// Active notifications
    notifications: RwSignal<Vec<StatusNotification>>,

    /// Active progress indicators
    progress_indicators: RwSignal<Vec<ProgressIndicator>>,

    /// System status
    system_status: RwSignal<SystemStatus>,

    /// Status items (left side)
    left_items: RwSignal<Vec<StatusItem>>,

    /// Status items (right side)
    right_items: RwSignal<Vec<StatusItem>>,

    /// Current cursor position
    cursor_position: RwSignal<Option<(usize, usize)>>,

    /// Current file encoding
    file_encoding: RwSignal<Option<String>>,

    /// Current language mode
    language_mode: RwSignal<Option<String>>,

    /// Line ending type
    line_ending: RwSignal<Option<String>>,
}

/// Elemento della barra di stato
#[allow(dead_code)]
pub struct StatusItem {
    /// ID univoco
    pub id: Uuid,

    /// Testo dell'elemento
    pub text: RwSignal<String>,

    /// Tipo di stato
    pub status_type: RwSignal<StatusType>,

    /// Tooltip dell'elemento
    pub tooltip: Option<String>,

    /// È cliccabile?
    pub clickable: bool,

    /// Callback quando viene cliccato
    pub on_click: Option<Arc<dyn Fn() + Send + Sync>>,

    /// Priorità per l'ordinamento
    pub priority: i32,

    /// Se l'elemento è visibile
    pub visible: RwSignal<bool>,
}

impl StatusItem {
    /// Crea un nuovo elemento della barra di stato
    #[allow(dead_code)]
    pub fn new(text: RwSignal<String>, status_type: RwSignal<StatusType>) -> Self {
        Self {
            id: Uuid::new_v4(),
            text,
            status_type,
            tooltip: None,
            clickable: false,
            on_click: None,
            priority: 0,
            visible: create_rw_signal(true),
        }
    }

    /// Aggiunge un tooltip
    #[allow(dead_code)]
    pub fn with_tooltip(mut self, tooltip: &str) -> Self {
        self.tooltip = Some(tooltip.to_string());
        self
    }

    /// Imposta la priorità
    #[allow(dead_code)]
    pub fn with_priority(mut self, priority: i32) -> Self {
        self.priority = priority;
        self
    }

    /// Rende l'elemento cliccabile
    #[allow(dead_code)]
    pub fn with_click<F>(mut self, callback: F) -> Self
    where
        F: Fn() + Send + Sync + 'static,
    {
        self.clickable = true;
        self.on_click = Some(Arc::new(callback));
        self
    }

    /// Imposta la visibilità
    #[allow(dead_code)]
    pub fn set_visible(&self, visible: bool) {
        self.visible.set(visible);
    }
}

impl StatusBarManager {
    /// Create a new status bar manager
    pub fn new(
        core: Arc<CoreEngine>,
        theme_manager: Arc<ThemeManager>,
        file_operations: Arc<FileOperationsEngine>,
    ) -> Self {
        let default_system_status = SystemStatus {
            cpu_usage: 0.0,
            memory_usage: 0.0,
            disk_usage: 0.0,
            network_activity: false,
            git_status: None,
            lsp_status: HashMap::new(),
            build_status: None,
        };

        Self {
            core,
            theme_manager,
            file_operations,
            notifications: create_rw_signal(Vec::new()),
            progress_indicators: create_rw_signal(Vec::new()),
            system_status: create_rw_signal(default_system_status),
            left_items: create_rw_signal(Vec::new()),
            right_items: create_rw_signal(Vec::new()),
            cursor_position: create_rw_signal(None),
            file_encoding: create_rw_signal(None),
            language_mode: create_rw_signal(None),
            line_ending: create_rw_signal(None),
        }
    }

    /// Add a notification
    pub fn add_notification(&self, message: String, notification_type: NotificationType) -> Uuid {
        let notification = StatusNotification {
            id: Uuid::new_v4(),
            message,
            notification_type,
            timestamp: Instant::now(),
            duration: Some(Duration::from_secs(5)), // Default 5 seconds
            dismissible: true,
            action: None,
        };

        let id = notification.id;
        self.notifications.update(|notifications| {
            notifications.push(notification);
        });

        id
    }

    /// Add a notification with action
    pub fn add_notification_with_action(
        &self,
        message: String,
        notification_type: NotificationType,
        action_label: String,
        action_callback: Arc<dyn Fn() + Send + Sync>,
    ) -> Uuid {
        let notification = StatusNotification {
            id: Uuid::new_v4(),
            message,
            notification_type,
            timestamp: Instant::now(),
            duration: Some(Duration::from_secs(10)), // Longer for actionable notifications
            dismissible: true,
            action: Some(StatusAction {
                label: action_label,
                callback: action_callback,
            }),
        };

        let id = notification.id;
        self.notifications.update(|notifications| {
            notifications.push(notification);
        });

        id
    }

    /// Remove a notification
    pub fn remove_notification(&self, id: Uuid) {
        self.notifications.update(|notifications| {
            notifications.retain(|n| n.id != id);
        });
    }

    /// Add a progress indicator
    pub fn add_progress(&self, label: String, indeterminate: bool) -> Uuid {
        let progress = ProgressIndicator {
            id: Uuid::new_v4(),
            label,
            progress: 0.0,
            indeterminate,
            cancellable: false,
            cancel_callback: None,
        };

        let id = progress.id;
        self.progress_indicators.update(|indicators| {
            indicators.push(progress);
        });

        id
    }

    /// Update progress
    pub fn update_progress(&self, id: Uuid, progress: f32, label: Option<String>) {
        self.progress_indicators.update(|indicators| {
            if let Some(indicator) = indicators.iter_mut().find(|i| i.id == id) {
                indicator.progress = progress.clamp(0.0, 1.0);
                if let Some(new_label) = label {
                    indicator.label = new_label;
                }
            }
        });
    }

    /// Remove a progress indicator
    pub fn remove_progress(&self, id: Uuid) {
        self.progress_indicators.update(|indicators| {
            indicators.retain(|i| i.id != id);
        });
    }

    /// Update cursor position
    pub fn update_cursor_position(&self, line: usize, column: usize) {
        self.cursor_position.set(Some((line, column)));
    }

    /// Update file encoding
    pub fn update_file_encoding(&self, encoding: Option<String>) {
        self.file_encoding.set(encoding);
    }

    /// Update language mode
    pub fn update_language_mode(&self, language: Option<String>) {
        self.language_mode.set(language);
    }

    /// Update line ending type
    pub fn update_line_ending(&self, line_ending: Option<String>) {
        self.line_ending.set(line_ending);
    }

    /// Update system status
    pub fn update_system_status(&self, status: SystemStatus) {
        self.system_status.set(status);
    }

    /// Add a status item to the left side
    pub fn add_left_item(&self, item: StatusItem) {
        self.left_items.update(|items| {
            items.push(item);
            items.sort_by_key(|item| item.priority);
        });
    }

    /// Add a status item to the right side
    pub fn add_right_item(&self, item: StatusItem) {
        self.right_items.update(|items| {
            items.push(item);
            items.sort_by_key(|item| item.priority);
        });
    }

    /// Remove a status item
    pub fn remove_item(&self, id: Uuid) {
        self.left_items.update(|items| {
            items.retain(|item| item.id != id);
        });
        self.right_items.update(|items| {
            items.retain(|item| item.id != id);
        });
    }
}

/// Create enhanced status bar with notifications and progress
pub fn enhanced_status_bar(manager: StatusBarManager) -> impl View {
    let theme = manager.theme_manager.get_active_theme().unwrap_or_default();
    let notifications = manager.notifications;
    let progress_indicators = manager.progress_indicators;
    let left_items = manager.left_items;
    let right_items = manager.right_items;
    let cursor_position = manager.cursor_position;
    let file_encoding = manager.file_encoding;
    let language_mode = manager.language_mode;
    let line_ending = manager.line_ending;
    let system_status = manager.system_status;

    v_stack((
        // Notifications bar (if any)
        dyn_stack(
            move || notifications.get(),
            |notification: &StatusNotification| notification.id,
            move |notification: StatusNotification| {
                create_notification_view(manager.theme_manager.clone(), notification, notifications)
            }
        ),

        // Main status bar
        h_stack((
            // Left side items
            h_stack((
                // Progress indicators
                dyn_stack(
                    move || progress_indicators.get(),
                    |progress: &ProgressIndicator| progress.id,
                    move |progress: ProgressIndicator| {
                        create_progress_view(manager.theme_manager.clone(), progress)
                    }
                ),

                // Custom left items
                dyn_stack(
                    move || left_items.get(),
                    |item: &StatusItem| item.id,
                    move |item: StatusItem| {
                        create_status_item_view(manager.theme_manager.clone(), item)
                    }
                ),
            ))
            .style(|s| s.gap(8.0)),

            // Flexible space
            empty().style(|s| s.flex_grow(1.0)),

            // Right side items
            h_stack((
                // System status indicators
                create_system_status_view(manager.theme_manager.clone(), system_status),

                // File info
                create_file_info_view(
                    manager.theme_manager.clone(),
                    cursor_position,
                    file_encoding,
                    language_mode,
                    line_ending,
                ),

                // Custom right items
                dyn_stack(
                    move || right_items.get(),
                    |item: &StatusItem| item.id,
                    move |item: StatusItem| {
                        create_status_item_view(manager.theme_manager.clone(), item)
                    }
                ),
            ))
            .style(|s| s.gap(8.0)),
        ))
        .style(move |s| {
            s.width_full()
             .height(28.0)
             .padding_horiz(8.0)
             .background(theme.colors.background_secondary)
             .border_top(theme.borders.width_thin)
             .border_color(theme.colors.border)
             .color(theme.colors.text_secondary)
             .font_size(theme.font_sizes.xs)
        }),
    ))
}

/// Crea la barra di stato (legacy function)
#[allow(dead_code)]
pub fn status_bar(
    theme_manager: Arc<ThemeManager>,
    left_items: Vec<StatusItem>,
    right_items: Vec<StatusItem>,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();

    h_stack((
        // Elementi a sinistra
        h_stack_from_iter(left_items.into_iter().map(|item| {
            create_status_item_view(theme_manager.clone(), item)
        }))
        .style(|s| s.gap(16.0)),

        // Spazio flessibile
        empty().style(|s| s.flex_grow(1.0)),

        // Elementi a destra
        h_stack_from_iter(right_items.into_iter().map(|item| {
            create_status_item_view(theme_manager.clone(), item)
        }))
        .style(|s| s.gap(16.0)),
    ))
    .style(move |s| {
        s.width_full()
         .height(28.0)
         .padding_horiz(8.0)
         .background(theme.colors.background_secondary)
         .border_top(theme.borders.width_thin)
         .border_color(theme.colors.border)
         .color(theme.colors.text_secondary)
         .font_size(theme.font_sizes.xs)
    })
}

/// Crea un elemento della barra di stato
#[allow(dead_code)]
fn create_status_item(
    theme_manager: Arc<ThemeManager>,
    item: StatusItem,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();
    let text = item.text;
    let status_type = item.status_type;
    let clickable = item.clickable;

    let view = label(move || text.get())
        .style(move |s| {
            let base_style = s.padding_horiz(4.0)
                             .padding_vert(2.0)
                             .font_size(theme.font_sizes.xs);

            // Applica stili in base al tipo di stato
            match status_type.get() {
                StatusType::Normal => base_style.color(theme.colors.text_secondary),
                StatusType::Success => base_style.color(theme.colors.success),
                StatusType::Error => base_style.color(theme.colors.error),
                StatusType::Warning => base_style.color(theme.colors.warning),
                StatusType::Info => base_style.color(theme.colors.info),
            }
        });

    // Se è cliccabile, aggiungiamo gli stili e il callback
    if clickable {
        if let Some(on_click) = item.on_click {
            container(view)
                .on_click_stop(move |_| {
                    on_click();
                })
                .style(move |s| {
                    s.cursor(floem::style::CursorStyle::Pointer)
                     .hover(|s| s.color(theme.colors.accent))
                })
        } else {
            container(view)
        }
    } else {
        container(view)
    }
}

/// Crea una barra di stato semplice con un messaggio
#[allow(dead_code)]
pub fn simple_status_bar(
    theme_manager: Arc<ThemeManager>,
    message: RwSignal<String>,
    status: RwSignal<StatusType>,
) -> impl View {
    let item = StatusItem::new(message, status);
    status_bar(theme_manager, vec![item], vec![])
}
