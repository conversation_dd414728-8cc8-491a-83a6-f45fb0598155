//! # Matrix UI
//! 
//! Layer UI di MATRIX IDE, che integra Lapce precompilato con Floem 0.2
//! per creare un'interfaccia utente moderna e reattiva.

mod app;
mod error;
pub mod theme;
pub mod components;
pub mod layout;
mod floem_widgets;
mod lapce_bridge;
mod floem_bridge;
pub mod editor;
pub mod panels;
mod plugin_host;
mod plugin_integrator;
pub mod quality;
pub mod performance;
pub mod file_operations;
pub mod framework;

pub use app::App;
pub use error::UiError;
pub use theme::{Theme, ThemeManager};
pub use floem_bridge::{FloemBridge, UiComponent};
pub use editor::{Editor, lapce::{LapceEditor, LapceConfig, WrapMode}};
pub use panels::*;
pub use plugin_host::PluginHost;
pub use plugin_integrator::{PluginHostIntegrator, PluginIntegrationError};
pub use quality::{QualityManager, QualityDashboard, QualityLevel, QualityMetric};
pub use performance::{PerformanceManager, PerformanceMetrics, PerformanceReport};
pub use file_operations::{FileOperationsEngine, FileOperationType, FileOperationResult, FileOperationProgress};

// Re-export commonly used components
pub use components::dag_viewer::DagViewer;
pub use components::advanced_graph_viewer::{AdvancedGraphViewer, GraphNode, GraphEdge, NodeType, EdgeType};
pub use components::code_analysis_panel::{CodeAnalysisPanel, CodeMetrics, CodeIssue};
pub use components::performance_monitor::{PerformanceMonitor, PerformanceMetric, DisplayMode};
pub use components::file_tree::{FileTree, FileTreeNode, FileType};
pub use components::properties_panel::{PropertiesPanel, Property, PropertyValue, PropertyType};
pub use layout::LayoutManager;

/// Versione del UI Layer
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Inizializza il UI Layer con la configurazione predefinita
pub fn init() -> Result<App, error::UiError> {
    App::new()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_app_creation() {
        let app = init();
        assert!(app.is_ok());
    }
}