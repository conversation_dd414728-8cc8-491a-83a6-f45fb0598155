//! Terminal Panel for MATRIX IDE
//!
//! This module implements an integrated terminal panel that provides command execution
//! capabilities within the IDE environment.

use std::sync::Arc;
use std::process::{Command, Stdio, Child};
use std::io::{BufReader, BufRead, Write};
use std::thread;
use std::sync::Mutex;
use std::collections::HashMap;
use regex::Regex;

use floem::{
    View, IntoView,
    views::{container, dyn_container, h_stack, h_stack_from_iter, label, v_stack, button, text_input, scroll, dyn_stack, Decorators},
    reactive::{create_rw_signal, RwSignal, SignalGet, SignalUpdate},
    keyboard::{Key, NamedKey},
    peniko::Color,
    text::Weight,
};

use matrix_core::Engine as CoreEngine;
use crate::theme::ThemeManager;
use crate::error::UiError;
use super::{Panel, PanelConfig, PanelPosition, PanelState};

/// ANSI color codes for terminal output
#[derive(Debug, Clone, PartialEq)]
pub struct AnsiColor {
    pub foreground: Option<Color>,
    pub background: Option<Color>,
    pub bold: bool,
    pub italic: bool,
    pub underline: bool,
}

impl Default for AnsiColor {
    fn default() -> Self {
        Self {
            foreground: None,
            background: None,
            bold: false,
            italic: false,
            underline: false,
        }
    }
}

/// Terminal output line with ANSI formatting
#[derive(Debug, Clone)]
pub struct TerminalLine {
    pub text: String,
    pub color: AnsiColor,
    pub timestamp: std::time::SystemTime,
}

impl TerminalLine {
    pub fn new(text: String) -> Self {
        Self {
            text,
            color: AnsiColor::default(),
            timestamp: std::time::SystemTime::now(),
        }
    }

    pub fn with_color(text: String, color: AnsiColor) -> Self {
        Self {
            text,
            color,
            timestamp: std::time::SystemTime::now(),
        }
    }
}

/// Terminal session state
#[derive(Clone)]
struct TerminalSession {
    /// Output lines with ANSI formatting
    output: RwSignal<Vec<TerminalLine>>,
    /// Current input
    input: RwSignal<String>,
    /// Running process
    process: Arc<Mutex<Option<Child>>>,
    /// Working directory
    working_dir: RwSignal<String>,
    /// Session ID
    session_id: String,
    /// Session title
    title: RwSignal<String>,
    /// Is session active
    is_active: RwSignal<bool>,
    /// Command history
    history: RwSignal<Vec<String>>,
    /// History index for navigation
    history_index: RwSignal<Option<usize>>,
}

impl TerminalSession {
    fn new(session_id: String) -> Self {
        let current_dir = std::env::current_dir()
            .map(|path| path.to_string_lossy().to_string())
            .unwrap_or_else(|_| ".".to_string());

        let welcome_line = TerminalLine::new(format!("Terminal Session {} - Directory: {}", session_id, current_dir));

        Self {
            output: create_rw_signal(vec![welcome_line]),
            input: create_rw_signal(String::new()),
            process: Arc::new(Mutex::new(None)),
            working_dir: create_rw_signal(current_dir),
            session_id: session_id.clone(),
            title: create_rw_signal(format!("Terminal {}", session_id)),
            is_active: create_rw_signal(false),
            history: create_rw_signal(Vec::new()),
            history_index: create_rw_signal(None),
        }
    }

    /// Add a line to the output with ANSI parsing
    fn add_output_line(&self, text: String) {
        let parsed_line = Self::parse_ansi_line(text);
        self.output.update(|output| {
            output.push(parsed_line);
            // Limit output to prevent memory issues
            if output.len() > 10000 {
                output.drain(0..1000);
            }
        });
    }

    /// Parse ANSI escape sequences in a line
    fn parse_ansi_line(text: String) -> TerminalLine {
        // Simple ANSI parser - can be enhanced for full VT100 support
        let ansi_regex = Regex::new(r"\x1b\[[0-9;]*m").unwrap();
        let clean_text = ansi_regex.replace_all(&text, "").to_string();

        // Extract color information (simplified)
        let mut color = AnsiColor::default();

        // Check for common ANSI codes
        if text.contains("\x1b[31m") { // Red
            color.foreground = Some(Color::rgb8(255, 85, 85));
        } else if text.contains("\x1b[32m") { // Green
            color.foreground = Some(Color::rgb8(85, 255, 85));
        } else if text.contains("\x1b[33m") { // Yellow
            color.foreground = Some(Color::rgb8(255, 255, 85));
        } else if text.contains("\x1b[34m") { // Blue
            color.foreground = Some(Color::rgb8(85, 85, 255));
        } else if text.contains("\x1b[35m") { // Magenta
            color.foreground = Some(Color::rgb8(255, 85, 255));
        } else if text.contains("\x1b[36m") { // Cyan
            color.foreground = Some(Color::rgb8(85, 255, 255));
        }

        if text.contains("\x1b[1m") { // Bold
            color.bold = true;
        }

        TerminalLine::with_color(clean_text, color)
    }

    /// Add command to history
    fn add_to_history(&self, command: String) {
        if !command.trim().is_empty() {
            self.history.update(|history| {
                // Avoid duplicates
                if history.last() != Some(&command) {
                    history.push(command);
                    // Limit history size
                    if history.len() > 1000 {
                        history.drain(0..100);
                    }
                }
            });
            self.history_index.set(None);
        }
    }

    /// Navigate history up
    fn history_up(&self) -> Option<String> {
        let history = self.history.get();
        if history.is_empty() {
            return None;
        }

        let current_index = self.history_index.get();
        let new_index = match current_index {
            None => Some(history.len() - 1),
            Some(idx) if idx > 0 => Some(idx - 1),
            Some(_) => Some(0),
        };

        self.history_index.set(new_index);
        new_index.and_then(|idx| history.get(idx).cloned())
    }

    /// Navigate history down
    fn history_down(&self) -> Option<String> {
        let history = self.history.get();
        let current_index = self.history_index.get()?;

        if current_index + 1 >= history.len() {
            self.history_index.set(None);
            Some(String::new())
        } else {
            let new_index = current_index + 1;
            self.history_index.set(Some(new_index));
            history.get(new_index).cloned()
        }
    }
}

/// Terminal split view mode
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum TerminalSplitMode {
    /// Single terminal view
    Single,
    /// Horizontal split (top/bottom)
    Horizontal,
    /// Vertical split (left/right)
    Vertical,
    /// Quad split (2x2 grid)
    Quad,
}

/// Build system integration
#[derive(Debug, Clone)]
pub struct BuildSystemConfig {
    /// Build commands for different project types
    pub build_commands: HashMap<String, Vec<String>>,
    /// Test commands
    pub test_commands: HashMap<String, Vec<String>>,
    /// Run commands
    pub run_commands: HashMap<String, Vec<String>>,
    /// Auto-detect project type
    pub auto_detect: bool,
}

impl Default for BuildSystemConfig {
    fn default() -> Self {
        let mut build_commands = HashMap::new();
        build_commands.insert("rust".to_string(), vec!["cargo".to_string(), "build".to_string()]);
        build_commands.insert("node".to_string(), vec!["npm".to_string(), "run".to_string(), "build".to_string()]);
        build_commands.insert("python".to_string(), vec!["python".to_string(), "setup.py".to_string(), "build".to_string()]);

        let mut test_commands = HashMap::new();
        test_commands.insert("rust".to_string(), vec!["cargo".to_string(), "test".to_string()]);
        test_commands.insert("node".to_string(), vec!["npm".to_string(), "test".to_string()]);
        test_commands.insert("python".to_string(), vec!["python".to_string(), "-m".to_string(), "pytest".to_string()]);

        let mut run_commands = HashMap::new();
        run_commands.insert("rust".to_string(), vec!["cargo".to_string(), "run".to_string()]);
        run_commands.insert("node".to_string(), vec!["npm".to_string(), "start".to_string()]);
        run_commands.insert("python".to_string(), vec!["python".to_string(), "main.py".to_string()]);

        Self {
            build_commands,
            test_commands,
            run_commands,
            auto_detect: true,
        }
    }
}

/// Terminal Panel that provides integrated command execution
#[derive(Clone)]
pub struct TerminalPanel {
    /// Core Engine
    core: Arc<CoreEngine>,
    /// Theme Manager
    theme_manager: Arc<ThemeManager>,
    /// Active terminal sessions
    sessions: RwSignal<Vec<TerminalSession>>,
    /// Currently active session index
    active_session: RwSignal<usize>,
    /// Panel configuration
    config: PanelConfig,
    /// Split view mode
    split_mode: RwSignal<TerminalSplitMode>,
    /// Build system configuration
    build_config: RwSignal<BuildSystemConfig>,
    /// Session counter for unique IDs
    session_counter: RwSignal<usize>,
}

impl TerminalPanel {
    /// Create a new terminal panel
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        // Create initial terminal session
        let initial_session = TerminalSession::new("1".to_string());
        let sessions = create_rw_signal(vec![initial_session]);
        let active_session = create_rw_signal(0);

        let config = PanelConfig {
            id: "terminal".to_string(),
            position: PanelPosition::Bottom,
            state: PanelState::Visible,
            size: (300.0, 300.0),
            resizable: true,
            closable: true,
            movable: true,
            title: "Terminal".to_string(),
        };

        Ok(Self {
            core,
            theme_manager,
            sessions,
            active_session,
            config,
            split_mode: create_rw_signal(TerminalSplitMode::Single),
            build_config: create_rw_signal(BuildSystemConfig::default()),
            session_counter: create_rw_signal(1),
        })
    }

    /// Add a new terminal session
    pub fn add_session(&self) -> usize {
        let session_id = self.session_counter.get() + 1;
        self.session_counter.set(session_id);

        let new_session = TerminalSession::new(session_id.to_string());
        let session_index = self.sessions.get().len();

        self.sessions.update(|sessions| {
            sessions.push(new_session);
        });

        self.active_session.set(session_index);
        session_index
    }

    /// Close a terminal session
    pub fn close_session(&self, index: usize) {
        self.sessions.update(|sessions| {
            if sessions.len() > 1 && index < sessions.len() {
                sessions.remove(index);

                // Adjust active session if needed
                let current_active = self.active_session.get();
                if current_active >= sessions.len() {
                    self.active_session.set(sessions.len().saturating_sub(1));
                } else if current_active > index {
                    self.active_session.set(current_active - 1);
                }
            }
        });
    }

    /// Switch to a specific session
    pub fn switch_to_session(&self, index: usize) {
        let sessions_len = self.sessions.get().len();
        if index < sessions_len {
            self.active_session.set(index);
        }
    }

    /// Set split mode
    pub fn set_split_mode(&self, mode: TerminalSplitMode) {
        self.split_mode.set(mode);
    }

    /// Detect project type for build system integration
    pub fn detect_project_type(&self, working_dir: &str) -> Option<String> {
        let path = std::path::Path::new(working_dir);

        if path.join("Cargo.toml").exists() {
            Some("rust".to_string())
        } else if path.join("package.json").exists() {
            Some("node".to_string())
        } else if path.join("setup.py").exists() || path.join("pyproject.toml").exists() {
            Some("python".to_string())
        } else if path.join("Makefile").exists() {
            Some("make".to_string())
        } else {
            None
        }
    }

    /// Execute build command for current project
    pub fn execute_build(&self) {
        if let Some(session) = self.get_active_session() {
            let working_dir = session.working_dir.get();
            if let Some(project_type) = self.detect_project_type(&working_dir) {
                let build_config = self.build_config.get();
                if let Some(build_cmd) = build_config.build_commands.get(&project_type) {
                    let command = build_cmd.join(" ");
                    session.add_output_line(format!("🔨 Building {} project...", project_type));
                    Self::execute_command_in_session(command, session);
                }
            }
        }
    }

    /// Execute test command for current project
    pub fn execute_test(&self) {
        if let Some(session) = self.get_active_session() {
            let working_dir = session.working_dir.get();
            if let Some(project_type) = self.detect_project_type(&working_dir) {
                let build_config = self.build_config.get();
                if let Some(test_cmd) = build_config.test_commands.get(&project_type) {
                    let command = test_cmd.join(" ");
                    session.add_output_line(format!("🧪 Testing {} project...", project_type));
                    Self::execute_command_in_session(command, session);
                }
            }
        }
    }

    /// Execute run command for current project
    pub fn execute_run(&self) {
        if let Some(session) = self.get_active_session() {
            let working_dir = session.working_dir.get();
            if let Some(project_type) = self.detect_project_type(&working_dir) {
                let build_config = self.build_config.get();
                if let Some(run_cmd) = build_config.run_commands.get(&project_type) {
                    let command = run_cmd.join(" ");
                    session.add_output_line(format!("🚀 Running {} project...", project_type));
                    Self::execute_command_in_session(command, session);
                }
            }
        }
    }

    /// Get the currently active session
    fn get_active_session(&self) -> Option<TerminalSession> {
        let sessions = self.sessions.get();
        let active_index = self.active_session.get();
        sessions.get(active_index).cloned()
    }

    /// Execute a command in the active terminal session
    pub fn execute_command(&self, command: String) {
        if let Some(session) = self.get_active_session() {
            // Add command to history
            session.add_to_history(command.clone());

            // Add command to output
            session.add_output_line(format!("$ {}", command));

            Self::execute_command_in_session(command, session.clone());

            // Clear input
            session.input.set(String::new());
        }
    }

    /// Execute a command in a specific session
    fn execute_command_in_session(command: String, session: TerminalSession) {
        let working_dir = session.working_dir.get();

        thread::spawn(move || {
            let output = Command::new("sh")
                .arg("-c")
                .arg(&command)
                .current_dir(&working_dir)
                .output();

            match output {
                Ok(output) => {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    let stderr = String::from_utf8_lossy(&output.stderr);

                    for line in stdout.lines() {
                        session.add_output_line(line.to_string());
                    }
                    for line in stderr.lines() {
                        session.add_output_line(format!("ERROR: {}", line));
                    }

                    // Add exit status
                    if !output.status.success() {
                        session.add_output_line(format!("Process exited with code: {}",
                            output.status.code().unwrap_or(-1)));
                    }
                }
                Err(e) => {
                    session.add_output_line(format!("Failed to execute command: {}", e));
                }
            }
        });
    }

    /// Create the terminal panel view
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let sessions = self.sessions;
        let active_session = self.active_session;
        let self_clone = self.clone();

        container(
            v_stack((
                // Terminal header with tabs and controls
                self.create_header(),
                
                // Active terminal session view
                dyn_container(
                    move || (sessions.get(), active_session.get()),
                    move |(sessions_vec, active_idx)| {
                        if let Some(session) = sessions_vec.get(active_idx) {
                            self_clone.create_session_view(session).into_any()
                        } else {
                            label(|| "No active terminal session".to_string()).into_any()
                        }
                    }
                )
                .style(|s| s.flex_grow(1.0))
            ))
        )
        .style(move |s| {
            s.size_full()
             .background(theme.colors.background)
             .border_color(theme.colors.border)
        })
    }

    /// Create the terminal header with tabs and controls
    fn create_header(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let sessions = self.sessions;
        let active_session = self.active_session;

        container(
            h_stack((
                // Terminal tabs
                dyn_container(
                    move || (sessions.get(), active_session.get()),
                    move |(sessions_vec, active_idx)| {
                        h_stack_from_iter(
                            sessions_vec.iter().enumerate().map(|(idx, session)| {
                                let is_active = idx == active_idx;
                                let session_id = session.session_id.clone();

                                button(label(move || format!("Terminal {}", session_id)))
                                    .action(move || {
                                        active_session.set(idx);
                                    })
                                    .style(move |s| {
                                        let mut style = s.padding(8.0).margin_right(4.0);
                                        if is_active {
                                            style = style.background(theme.colors.active);
                                        }
                                        style
                                    })
                            })
                        ).into_any()
                    }
                )
                .style(|s| s.flex_grow(1.0)),

                // Control buttons
                h_stack((
                    // Add new terminal tab
                    button(label(|| "➕"))
                        .action({
                            let panel = self.clone();
                            move || {
                                panel.add_session();
                            }
                        })
                        .style(|s| s.padding(4.0).margin_right(4.0)),

                    // Split view controls
                    button(label(|| "⬌"))
                        .action({
                            let panel = self.clone();
                            move || {
                                panel.set_split_mode(TerminalSplitMode::Horizontal);
                            }
                        })
                        .style(|s| s.padding(4.0).margin_right(4.0)),

                    button(label(|| "⬍"))
                        .action({
                            let panel = self.clone();
                            move || {
                                panel.set_split_mode(TerminalSplitMode::Vertical);
                            }
                        })
                        .style(|s| s.padding(4.0).margin_right(4.0)),

                    // Build system controls
                    button(label(|| "🔨"))
                        .action({
                            let panel = self.clone();
                            move || {
                                panel.execute_build();
                            }
                        })
                        .style(|s| s.padding(4.0).margin_right(4.0)),

                    button(label(|| "🧪"))
                        .action({
                            let panel = self.clone();
                            move || {
                                panel.execute_test();
                            }
                        })
                        .style(|s| s.padding(4.0).margin_right(4.0)),

                    button(label(|| "🚀"))
                        .action({
                            let panel = self.clone();
                            move || {
                                panel.execute_run();
                            }
                        })
                        .style(|s| s.padding(4.0).margin_right(4.0)),

                    // Close current tab
                    button(label(|| "❌"))
                        .action({
                            let sessions = sessions;
                            let active_session = active_session;
                            move || {
                                let active_idx = active_session.get();
                                sessions.update(|s| {
                                    if s.len() > 1 && active_idx < s.len() {
                                        s.remove(active_idx);
                                        if active_idx > 0 {
                                            active_session.set(active_idx - 1);
                                        }
                                    }
                                });
                            }
                        })
                        .style(|s| s.padding(4.0))
                ))
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(8.0)
             .background(theme.colors.surface)
             .border_bottom(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Create view for a terminal session
    fn create_session_view(&self, session: &TerminalSession) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let output = session.output;
        let input = session.input;
        let working_dir = session.working_dir;
        let process = session.process.clone();

        v_stack((
            // Working directory display
            container(
                label(move || format!("Working Directory: {}", working_dir.get()))
                    .style(move |s| {
                        s.font_size(12.0)
                         .color(theme.colors.text_secondary)
                         .margin(4.0)
                    })
            ),

            // Terminal output area with ANSI color support
            scroll(
                dyn_stack(
                    move || output.get(),
                    |terminal_line: &TerminalLine| terminal_line.text.clone(),
                    move |terminal_line: TerminalLine| {
                        let line_color = terminal_line.color.clone();
                        let line_text = terminal_line.text.clone();

                        label(move || line_text.clone())
                            .style(move |s| {
                                let mut style = s
                                    .width_full()
                                    .font_family("Fira Code, Consolas, monospace".to_string())
                                    .font_size(13.0)
                                    .padding_vert(1.0);

                                // Apply ANSI colors
                                if let Some(fg_color) = line_color.foreground {
                                    style = style.color(fg_color);
                                } else {
                                    style = style.color(theme.colors.text);
                                }

                                if let Some(bg_color) = line_color.background {
                                    style = style.background(bg_color);
                                }

                                // Apply text formatting
                                if line_color.bold {
                                    style = style.font_weight(Weight::BOLD);
                                }

                                style
                            })
                    }
                )
                .style(|s| s.width_full())
            )
            .style(|s| s.flex_grow(1.0).width_full()),

            // Command input area
            container(
                h_stack((
                    label(|| "> ")
                        .style(move |s| {
                            s.font_family("Fira Code, Consolas, monospace".to_string())
                             .color(theme.colors.text)
                        }),
                        
                    text_input(input)
                        .on_key_down(
                            Key::Named(NamedKey::Enter),
                            |_| true,
                            {
                                let session_clone = session.clone();
                                let panel = self.clone();
                                move |_| {
                                    let command = session_clone.input.get();
                                    if !command.trim().is_empty() {
                                        panel.execute_command(command);
                                    }
                                }
                            }
                        )
                        .on_key_down(
                            Key::Named(NamedKey::ArrowUp),
                            |_| true,
                            {
                                let session_clone = session.clone();
                                move |_| {
                                    if let Some(prev_command) = session_clone.history_up() {
                                        session_clone.input.set(prev_command);
                                    }
                                }
                            }
                        )
                        .on_key_down(
                            Key::Named(NamedKey::ArrowDown),
                            |_| true,
                            {
                                let session_clone = session.clone();
                                move |_| {
                                    if let Some(next_command) = session_clone.history_down() {
                                        session_clone.input.set(next_command);
                                    }
                                }
                            }
                        )
                        .style(move |s| {
                            s.flex_grow(1.0)
                             .padding(4.0)
                             .font_family("Fira Code, Consolas, monospace".to_string())
                             .background(theme.colors.surface)
                             .border(1.0)
                             .border_color(theme.colors.border)
                             .border_radius(4.0)
                        })
                ))
            )
            .style(move |s| {
                s.width_full()
                 .padding(8.0)
                 .background(theme.colors.surface)
                 .border_top(1.0)
                 .border_color(theme.colors.border)
            })
        ))
        .style(|s| s.size_full())
    }


}

impl Panel for TerminalPanel {
    fn id(&self) -> &str {
        "terminal"
    }

    fn title(&self) -> &str {
        &self.config.title
    }

    fn create_view(&self) -> Box<dyn View> {
        Box::new(self.create_view())
    }

    fn update(&mut self) -> Result<(), UiError> {
        // Update terminal state if needed
        Ok(())
    }
}
