/// MATRIX_IDE - Professional Main Entry Point
///
/// Questo è il punto di ingresso principale per MATRIX_IDE.
/// Avvia l'applicazione con il nuovo framework UI professionale.
use log::{info, error};
use matrix_ui::{App, framework::MatrixUIFramework, theme::{Theme, ThemeManager}};
use std::sync::Arc;

fn main() {
    // Inizializza il logging
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();

    info!("🚀 Avvio MATRIX_IDE Professional...");

    // Inizializza il framework UI professionale
    info!("🎨 Inizializzazione Framework UI Professionale...");
    let ui_framework = match MatrixUIFramework::new() {
        Ok(framework) => {
            info!("✅ Framework UI creato con successo");
            framework
        }
        Err(e) => {
            error!("❌ Errore nella creazione del framework UI: {}", e);
            return;
        }
    };

    // Mostra informazioni sui temi disponibili
    info!("🎨 Temi disponibili:");
    info!("   • MATRIX Professional Dark - Tema scuro professionale");
    info!("   • MATRIX Professional Light - Tema chiaro professionale");

    // Su macOS, l'EventLoop deve essere creato sul thread principale
    let rt = tokio::runtime::Runtime::new().expect("Failed to create Tokio runtime");

    let result = rt.block_on(async {
        // Inizializza l'applicazione UI tradizionale (per compatibilità)
        info!("🔧 Inizializzazione App Legacy...");
        let mut app = match App::new() {
            Ok(app) => {
                info!("✅ App legacy creata con successo");
                app
            }
            Err(e) => {
                error!("❌ Errore nella creazione dell'app legacy: {}", e);
                return Err(e);
            }
        };

        // Inizializza tutti i componenti
        info!("⚙️ Inizializzazione componenti...");
        app.initialize().await?;
        info!("✅ Componenti inizializzati con successo");

        info!("🌟 MATRIX_IDE Professional avviato con successo!");
        info!("📋 Funzionalità Professional disponibili:");
        info!("   • Framework UI Modulare - Architettura componenti avanzata");
        info!("   • Sistema Design Professionale - Colori, tipografia, spaziatura");
        info!("   • Layout Resizable - Pannelli ridimensionabili con splitter");
        info!("   • Temi Professionali - Dark e Light mode");
        info!("   • Sistema Animazioni - Transizioni fluide");
        info!("   • DAG Viewer - Visualizzazione grafi interattiva");
        info!("   • AI Panel - Comunicazione con agenti AI");
        info!("   • Plugin System - Sistema di plugin modulare");
        info!("   • God Mode Panel - Funzionalità avanzate");

        Ok(app)
    });

    match result {
        Ok(app) => {
            // Avvia l'applicazione sul thread principale (necessario per macOS)
            info!("🎯 Avvio interfaccia grafica...");
            if let Err(e) = app.run() {
                error!("❌ Errore durante l'esecuzione dell'UI: {}", e);
                std::process::exit(1);
            }
        }
        Err(e) => {
            error!("❌ Errore durante l'inizializzazione: {}", e);
            std::process::exit(1);
        }
    }

    info!("👋 MATRIX_IDE terminato correttamente");
}


