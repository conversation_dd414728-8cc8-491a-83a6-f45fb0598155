//! Layout principale dell'applicazione
//!
//! Questo modulo definisce il layout principale dell'applicazione UI,
//! integrando tutti i componenti in un'interfaccia coesa.

use std::sync::Arc;
use floem::View;
use floem::views::{container, h_stack, v_stack, Decorators};

use crate::{
    error::UiError,
    theme::ThemeManager,
    panels::PanelManager,
    components::title_bar::matrix_title_bar,
};

/// Gestore del layout dell'applicazione
pub struct LayoutManager {
    /// Configurazione del layout
    config: LayoutConfig,
}

/// Configurazione del layout
#[derive(Debug, Clone)]
pub struct LayoutConfig {
    /// Larghezza minima dei pannelli
    pub min_panel_width: f64,
    /// Altezza minima dei pannelli
    pub min_panel_height: f64,
    /// Margini predefiniti
    pub default_margin: f64,
}

impl Default for LayoutConfig {
    fn default() -> Self {
        Self {
            min_panel_width: 200.0,
            min_panel_height: 100.0,
            default_margin: 8.0,
        }
    }
}

impl LayoutManager {
    /// Crea un nuovo gestore del layout
    pub fn new() -> Self {
        Self {
            config: LayoutConfig::default(),
        }
    }

    /// Crea un nuovo gestore del layout con configurazione personalizzata
    pub fn with_config(config: LayoutConfig) -> Self {
        Self { config }
    }

    /// Ottiene la configurazione corrente
    pub fn config(&self) -> &LayoutConfig {
        &self.config
    }

    /// Aggiorna la configurazione
    pub fn set_config(&mut self, config: LayoutConfig) {
        self.config = config;
    }
}

/// Layout principale dell'applicazione
pub struct MainLayout {
    /// Gestore dei pannelli
    panel_manager: Arc<PanelManager>,

    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,
}

impl MainLayout {
    /// Crea un nuovo layout principale
    pub fn new(
        panel_manager: Arc<PanelManager>,
        theme_manager: Arc<ThemeManager>,
    ) -> Result<Self, UiError> {
        Ok(Self {
            panel_manager,
            theme_manager,
        })
    }

    /// Costruisce il layout principale con design professionale
    pub fn build(&self) -> Result<impl View, UiError> {
        use floem::views::{h_stack, v_stack, container, stack, Decorators};
        use floem::reactive::{create_rw_signal, SignalGet, SignalUpdate};
        use floem::style::{Position, CursorStyle};
        use floem::event::{Event, EventListener, EventPropagation};

        let theme = self.theme_manager.get_active_theme()?;

        // Ottiene i pannelli dal gestore
        let left_panel = self.panel_manager.get_left_panel()?;
        let right_panel = self.panel_manager.get_right_panel()?;
        let bottom_panel = self.panel_manager.get_bottom_panel()?;
        let editor_panel = self.panel_manager.get_editor_panel()?;

        // Signals per le dimensioni dei pannelli (resizable)
        let left_panel_width = create_rw_signal(280.0);
        let right_panel_width = create_rw_signal(320.0);
        let bottom_panel_height = create_rw_signal(240.0);
        let is_left_dragging = create_rw_signal(false);
        let is_right_dragging = create_rw_signal(false);
        let is_bottom_dragging = create_rw_signal(false);

        // Title bar
        let title_bar = matrix_title_bar(self.theme_manager.clone())?;

        // === PANNELLO SINISTRO CON SPLITTER ===
        let left_panel_styled = container(left_panel)
            .style({
                let bg_color = theme.colors.background_secondary;
                let border_color = theme.colors.border;
                move |s| {
                    s.width(left_panel_width.get())
                        .height_full()
                        .background(bg_color)
                        .border_right(1.0)
                        .border_color(border_color)
                }
            });

        // Splitter verticale sinistro
        let left_splitter = container(floem::views::empty())
            .style({
                let border_color = theme.colors.accent;
                move |s| {
                    s.position(Position::Absolute)
                        .z_index(10)
                        .inset_top(0.0)
                        .inset_bottom(0.0)
                        .inset_left(left_panel_width.get())
                        .width(4.0)
                        .cursor(CursorStyle::ColResize)
                        .hover(|s| s.background(border_color))
                        .apply_if(is_left_dragging.get(), |s| s.background(border_color))
                }
            })
            .on_event(EventListener::PointerDown, move |_| {
                is_left_dragging.set(true);
                EventPropagation::Continue
            });

        // === AREA CENTRALE CON EDITOR E PANNELLO INFERIORE ===
        let editor_styled = container(editor_panel)
            .style({
                let bg_color = theme.colors.background;
                move |s| {
                    s.flex_grow(1.0)
                        .background(bg_color)
                }
            });

        let bottom_panel_styled = container(bottom_panel)
            .style({
                let bg_color = theme.colors.background_secondary;
                let border_color = theme.colors.border;
                move |s| {
                    s.height(bottom_panel_height.get())
                        .width_full()
                        .background(bg_color)
                        .border_top(1.0)
                        .border_color(border_color)
                }
            });

        // Splitter orizzontale inferiore
        let bottom_splitter = container(floem::views::empty())
            .style({
                let border_color = theme.colors.accent;
                move |s| {
                    s.position(Position::Absolute)
                        .z_index(10)
                        .inset_left(left_panel_width.get() + 4.0)
                        .inset_right(right_panel_width.get() + 4.0)
                        .inset_bottom(bottom_panel_height.get())
                        .height(4.0)
                        .cursor(CursorStyle::RowResize)
                        .hover(|s| s.background(border_color))
                        .apply_if(is_bottom_dragging.get(), |s| s.background(border_color))
                }
            })
            .on_event(EventListener::PointerDown, move |_| {
                is_bottom_dragging.set(true);
                EventPropagation::Continue
            });

        let center_area = v_stack((
            editor_styled,
            bottom_panel_styled,
        ))
        .style(|s| s.flex_grow(1.0).height_full());

        // === PANNELLO DESTRO CON SPLITTER ===
        let right_panel_styled = container(right_panel)
            .style({
                let bg_color = theme.colors.background_secondary;
                let border_color = theme.colors.border;
                move |s| {
                    s.width(right_panel_width.get())
                        .height_full()
                        .background(bg_color)
                        .border_left(1.0)
                        .border_color(border_color)
                }
            });

        // Splitter verticale destro
        let right_splitter = container(floem::views::empty())
            .style({
                let border_color = theme.colors.accent;
                move |s| {
                    s.position(Position::Absolute)
                        .z_index(10)
                        .inset_top(0.0)
                        .inset_bottom(0.0)
                        .inset_right(right_panel_width.get())
                        .width(4.0)
                        .cursor(CursorStyle::ColResize)
                        .hover(|s| s.background(border_color))
                        .apply_if(is_right_dragging.get(), |s| s.background(border_color))
                }
            })
            .on_event(EventListener::PointerDown, move |_| {
                is_right_dragging.set(true);
                EventPropagation::Continue
            });

        // === LAYOUT PRINCIPALE ===
        let main_content = stack((
            h_stack((
                left_panel_styled,
                center_area,
                right_panel_styled,
            ))
            .style(|s| s.flex_grow(1.0).width_full()),

            // Splitters overlay
            left_splitter,
            right_splitter,
            bottom_splitter,
        ));

        let main_layout = v_stack((
            title_bar,
            main_content,
        ))
        .style({
            let background_color = theme.colors.background;
            move |s| {
                s.size_full()
                    .background(background_color)
            }
        })
        .on_event(EventListener::PointerMove, move |event| {
            let pos = match event {
                Event::PointerMove(p) => p.pos,
                _ => return EventPropagation::Continue,
            };

            // Handle left panel resizing
            if is_left_dragging.get() {
                let new_width = pos.x.max(200.0).min(500.0);
                left_panel_width.set(new_width);
            }

            // Handle right panel resizing
            if is_right_dragging.get() {
                // Calculate from right edge
                let window_width = 1200.0; // TODO: Get actual window width
                let new_width = (window_width - pos.x).max(200.0).min(500.0);
                right_panel_width.set(new_width);
            }

            // Handle bottom panel resizing
            if is_bottom_dragging.get() {
                let window_height = 800.0; // TODO: Get actual window height
                let new_height = (window_height - pos.y).max(100.0).min(400.0);
                bottom_panel_height.set(new_height);
            }

            EventPropagation::Continue
        })
        .on_event(EventListener::PointerUp, move |_| {
            is_left_dragging.set(false);
            is_right_dragging.set(false);
            is_bottom_dragging.set(false);
            EventPropagation::Continue
        });

        Ok(main_layout)
    }
}
